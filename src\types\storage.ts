// Storage interfaces and types

export interface StorageCapabilities {
  supportsBiometrics: boolean;
  supportsHardwareKeystore: boolean;
  supportsFileEncryption: boolean;
  maxStorageSize?: number;
}

export interface PlatformStorageConfig {
  vaultDirectory: string;
  vaultFileName: string;
  preferencesKey: string;
  biometricKeyAlias: string;
  excludeFromBackup: boolean;
}

export interface StorageResult<T> {
  success: boolean;
  data?: T;
  error?: StorageError;
}

export interface StorageError extends Error {
  code: StorageErrorCode;
  platform: string;
  recoverable: boolean;
}

export type StorageErrorCode =
  | 'STORAGE_UNAVAILABLE'
  | 'PERMISSION_DENIED'
  | 'STORAGE_FULL'
  | 'FILE_NOT_FOUND'
  | 'CORRUPTION_DETECTED'
  | 'KEYCHAIN_ACCESS_DENIED'
  | 'BIOMETRIC_UNAVAILABLE'
  | 'DEVICE_LOCKED'
  | 'SECURITY_VIOLATION';

// Platform storage interface
export interface PlatformStorage {
  // Vault operations
  saveVault(vault: EncryptedVault): Promise<StorageResult<void>>;
  loadVault(): Promise<StorageResult<EncryptedVault>>;
  deleteVault(): Promise<StorageResult<void>>;
  vaultExists(): Promise<boolean>;
  
  // Biometric key operations
  saveBiometricKey(keyData: string): Promise<StorageResult<void>>;
  loadBiometricKey(): Promise<StorageResult<string>>;
  deleteBiometricKey(): Promise<StorageResult<void>>;
  biometricKeyExists(): Promise<boolean>;
  
  // Preferences
  savePreferences(preferences: UserPreferences): Promise<StorageResult<void>>;
  loadPreferences(): Promise<StorageResult<UserPreferences>>;
  
  // Platform capabilities
  getCapabilities(): Promise<StorageCapabilities>;
  getStorageInfo(): Promise<StorageInfo>;
}

export interface StorageInfo {
  availableSpace: number;
  usedSpace: number;
  vaultSize: number;
  lastBackup?: number;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  autoLockTimeout: number;
  biometricEnabled: boolean;
  syncEnabled: boolean;
  autoFillEnabled: boolean;
  securityNotifications: boolean;
  backupReminder: boolean;
  language: string;
  lastSeenVersion?: string;
}

// Backup and restore
export interface BackupMetadata {
  id: string;
  timestamp: number;
  platform: string;
  appVersion: string;
  vaultVersion: number;
  entryCount: number;
  encrypted: boolean;
  size: number;
}

export interface BackupFile {
  metadata: BackupMetadata;
  data: string; // Base64 encoded encrypted vault data
  checksum: string;
}

export interface RestoreOptions {
  mergeWithExisting: boolean;
  overwriteExisting: boolean;
  validateIntegrity: boolean;
}

// File system operations
export interface FileSystemInfo {
  path: string;
  size: number;
  createdAt: number;
  modifiedAt: number;
  permissions: FilePermissions;
}

export interface FilePermissions {
  readable: boolean;
  writable: boolean;
  encrypted: boolean;
  backupExcluded: boolean;
}