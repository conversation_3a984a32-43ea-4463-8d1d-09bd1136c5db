// API configuration

export const API_CONFIG = {
  supabase: {
    url: process.env.EXPO_PUBLIC_SUPABASE_URL || '',
    anonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '',
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  },
  
  endpoints: {
    vaults: 'vaults',
    syncLog: 'vault_sync_log',
    userPreferences: 'user_preferences',
  },
  
  timeout: 30000, // 30 seconds
  retryConfig: {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
  },
  
  headers: {
    'User-Agent': 'TWOFAI/1.0',
    'Content-Type': 'application/json',
  },
} as const;

// Environment-specific configuration
export const getApiConfig = (environment: 'development' | 'production' = 'production') => {
  const baseConfig = {
    ...API_CONFIG,
    environment,
  };

  if (environment === 'development') {
    return {
      ...baseConfig,
      timeout: 60000, // Longer timeout for development
      enableDebugLogs: true,
      strictSSL: false, // For local development
    };
  }

  return {
    ...baseConfig,
    enableDebugLogs: false,
    strictSSL: true,
  };
};

// Sync configuration
export const SYNC_CONFIG = {
  defaultInterval: 15, // minutes
  maxInterval: 24 * 60, // 24 hours
  minInterval: 1, // 1 minute
  
  conflictResolution: {
    defaultStrategy: 'last_write_wins' as const,
    timeoutMs: 30000,
    maxConflictAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
  
  offlineQueue: {
    maxSize: 100,
    persistToDisk: true,
    retryInterval: 5 * 60 * 1000, // 5 minutes
  },
  
  networkRequirements: {
    requireWifi: false,
    maxCellularSize: 1024 * 1024, // 1MB
    respectDataSaver: true,
  },
} as const;

// API error codes and messages
export const API_ERRORS = {
  NETWORK_ERROR: 'Network connection failed',
  TIMEOUT_ERROR: 'Request timed out',
  AUTH_ERROR: 'Authentication failed',
  FORBIDDEN_ERROR: 'Access denied',
  NOT_FOUND_ERROR: 'Resource not found',
  CONFLICT_ERROR: 'Data conflict detected',
  QUOTA_EXCEEDED: 'Storage quota exceeded',
  RATE_LIMITED: 'Too many requests',
  SERVER_ERROR: 'Server error occurred',
  UNKNOWN_ERROR: 'An unknown error occurred',
} as const;

// Request/Response types
export interface ApiResponse<T = any> {
  data?: T;
  error?: {
    message: string;
    code: string;
    details?: any;
  };
  meta?: {
    timestamp: number;
    requestId: string;
  };
}

export interface ApiRequestConfig {
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
  params?: Record<string, any>;
  auth?: boolean;
}