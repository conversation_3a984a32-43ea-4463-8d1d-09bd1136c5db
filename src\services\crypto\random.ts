import * as Crypto from 'expo-crypto';
import { CryptoError } from '../../types/crypto';

/**
 * Secure random number generation service
 */
export class RandomService {
  private static entropyPool: Uint8Array | null = null;
  private static poolIndex = 0;
  private static readonly POOL_SIZE = 1024;

  /**
   * Generate cryptographically secure random bytes
   */
  static generateSecureRandom(length: number): Uint8Array {
    try {
      return crypto.getRandomValues(new Uint8Array(length));
    } catch (error) {
      throw new CryptoError(
        `Random generation failed: ${error.message}`,
        'INSUFFICIENT_ENTROPY',
        'critical'
      );
    }
  }

  /**
   * Generate secure random string with custom alphabet
   */
  static generateRandomString(length: number, alphabet?: string): string {
    const defaultAlphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const chars = alphabet || defaultAlphabet;
    const randomBytes = this.generateSecureRandom(length);
    
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars[randomBytes[i] % chars.length];
    }
    
    return result;
  }

  /**
   * Generate secure random ID
   */
  static generateId(): string {
    const timestamp = Date.now().toString(36);
    const randomPart = this.generateRandomString(8);
    return `${timestamp}-${randomPart}`;
  }

  /**
   * Generate device ID
   */
  static generateDeviceId(): string {
    return this.generateRandomString(32, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');
  }

  /**
   * Generate secure IV for encryption
   */
  static generateIV(length: number = 12): Uint8Array {
    return this.generateSecureRandom(length);
  }

  /**
   * Generate secure salt
   */
  static generateSalt(length: number = 32): Uint8Array {
    return this.generateSecureRandom(length);
  }

  /**
   * Initialize entropy pool for high-frequency random operations
   */
  static initializeEntropyPool(): void {
    this.entropyPool = this.generateSecureRandom(this.POOL_SIZE);
    this.poolIndex = 0;
  }

  /**
   * Get random bytes from entropy pool (faster for frequent operations)
   */
  static getFromPool(length: number): Uint8Array {
    if (!this.entropyPool || this.poolIndex + length >= this.POOL_SIZE) {
      this.initializeEntropyPool();
    }

    const result = this.entropyPool!.slice(this.poolIndex, this.poolIndex + length);
    this.poolIndex += length;
    
    return result;
  }

  /**
   * Test randomness quality (for development/testing)
   */
  static testRandomness(samples: number = 10000): RandomnessTest {
    const values: number[] = [];
    
    for (let i = 0; i < samples; i++) {
      const randomByte = this.generateSecureRandom(1)[0];
      values.push(randomByte);
    }

    // Basic statistical tests
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const expectedMean = 127.5; // For bytes 0-255
    const meanDeviation = Math.abs(mean - expectedMean) / expectedMean;

    // Distribution test
    const buckets = new Array(256).fill(0);
    values.forEach(val => buckets[val]++);
    const expectedFreq = samples / 256;
    const chiSquare = buckets.reduce((sum, freq) => {
      const deviation = freq - expectedFreq;
      return sum + (deviation * deviation) / expectedFreq;
    }, 0);

    return {
      sampleSize: samples,
      mean,
      meanDeviation,
      chiSquare,
      uniformity: 1 - (meanDeviation * 2), // Rough uniformity measure
      passed: meanDeviation < 0.05 && chiSquare < 300, // Basic thresholds
    };
  }

  /**
   * Constant-time comparison for security-sensitive operations
   */
  static constant TimeCompare(a: Uint8Array, b: Uint8Array): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a[i] ^ b[i];
    }

    return result === 0;
  }

  /**
   * Secure array clearing
   */
  static secureWipe(array: Uint8Array): void {
    // Fill with random data first, then zeros
    const randomData = this.generateSecureRandom(array.length);
    array.set(randomData);
    array.fill(0);
  }
}

/**
 * Randomness test result interface
 */
interface RandomnessTest {
  sampleSize: number;
  mean: number;
  meanDeviation: number;
  chiSquare: number;
  uniformity: number;
  passed: boolean;
}

/**
 * Custom CryptoError class
 */
class CryptoError extends Error {
  constructor(
    message: string,
    public code: string,
    public severity: 'low' | 'medium' | 'high' | 'critical'
  ) {
    super(message);
    this.name = 'CryptoError';
  }
}

// Convenience exports
export const generateSecureRandom = RandomService.generateSecureRandom;
export const generateRandomString = RandomService.generateRandomString;
export const generateId = RandomService.generateId;
export const generateDeviceId = RandomService.generateDeviceId;
export const constantTimeCompare = RandomService.constantTimeCompare;