import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Copy, RefreshCw, Check } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import * as Clipboard from 'expo-clipboard';

interface TOTPDisplayProps {
  secret: string;
  issuer?: string;
  account: string;
  period?: number;
  digits?: number;
  algorithm?: 'SHA1' | 'SHA256' | 'SHA512';
  onCopy?: (code: string) => void;
}

export default function TOTPDisplay({
  secret,
  issuer,
  account,
  period = 30,
  digits = 6,
  algorithm = 'SHA1',
  onCopy,
}: TOTPDisplayProps) {
  const [currentCode, setCurrentCode] = useState('------');
  const [timeRemaining, setTimeRemaining] = useState(period);
  const [isGenerating, setIsGenerating] = useState(false);
  const [copied, setCopied] = useState(false);

  // Mock TOTP generation - replace with actual implementation
  const generateTOTP = useCallback(async (secret: string, timestamp?: number): Promise<string> => {
    // This is a placeholder - implement actual TOTP algorithm
    const time = timestamp || Math.floor(Date.now() / 1000);
    const counter = Math.floor(time / period);
    
    // Simple mock implementation for demonstration
    const mockCode = String(Math.floor(Math.random() * Math.pow(10, digits)))
      .padStart(digits, '0');
    
    return mockCode;
  }, [period, digits]);

  const updateCode = useCallback(async () => {
    setIsGenerating(true);
    try {
      const code = await generateTOTP(secret);
      setCurrentCode(code);
    } catch (error) {
      console.error('Failed to generate TOTP:', error);
      setCurrentCode('ERROR');
    } finally {
      setIsGenerating(false);
    }
  }, [secret, generateTOTP]);

  const updateTimeRemaining = useCallback(() => {
    const now = Math.floor(Date.now() / 1000);
    const remaining = period - (now % period);
    setTimeRemaining(remaining);
  }, [period]);

  const handleCopy = async () => {
    if (currentCode !== '------' && currentCode !== 'ERROR') {
      await Clipboard.setStringAsync(currentCode);
      setCopied(true);
      onCopy?.(currentCode);
      
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
      
      setTimeout(() => setCopied(false), 2000);
    }
  };

  useEffect(() => {
    // Initial code generation
    updateCode();
    updateTimeRemaining();

    // Set up intervals
    const codeInterval = setInterval(updateCode, period * 1000);
    const timeInterval = setInterval(updateTimeRemaining, 1000);

    return () => {
      clearInterval(codeInterval);
      clearInterval(timeInterval);
    };
  }, [updateCode, updateTimeRemaining, period]);

  const formatCode = (code: string) => {
    if (code.length === 6) {
      return `${code.substring(0, 3)} ${code.substring(3)}`;
    } else if (code.length === 8) {
      return `${code.substring(0, 4)} ${code.substring(4)}`;
    }
    return code;
  };

  const getProgressPercentage = () => {
    return ((period - timeRemaining) / period) * 100;
  };

  const getTimeColor = () => {
    if (timeRemaining <= 5) return '#ef4444'; // Red
    if (timeRemaining <= 10) return '#f59e0b'; // Amber
    return '#10b981'; // Green
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.accountInfo}>
          {issuer && (
            <Text style={styles.issuer} numberOfLines={1}>
              {issuer}
            </Text>
          )}
          <Text style={styles.account} numberOfLines={1}>
            {account}
          </Text>
        </View>
        
        <TouchableOpacity
          style={styles.copyButton}
          onPress={handleCopy}
          disabled={currentCode === '------' || currentCode === 'ERROR'}
        >
          {copied ? (
            <Check size={20} color="#10b981" />
          ) : (
            <Copy size={20} color="#6b7280" />
          )}
        </TouchableOpacity>
      </View>

      {/* TOTP Code */}
      <View style={styles.codeContainer}>
        <Text style={styles.code}>
          {formatCode(currentCode)}
        </Text>
        
        {isGenerating && (
          <View style={styles.refreshIcon}>
            <RefreshCw size={16} color="#6b7280" />
          </View>
        )}
      </View>

      {/* Progress and Timer */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { 
                width: `${getProgressPercentage()}%`,
                backgroundColor: getTimeColor(),
              }
            ]} 
          />
        </View>
        
        <Text style={[styles.timeRemaining, { color: getTimeColor() }]}>
          {timeRemaining}s
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#f1f5f9',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  accountInfo: {
    flex: 1,
    marginRight: 12,
  },
  issuer: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  account: {
    fontSize: 14,
    color: '#6b7280',
  },
  copyButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  codeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minHeight: 50,
  },
  code: {
    fontSize: 36,
    fontWeight: '700',
    color: '#1e293b',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    letterSpacing: 3,
  },
  refreshIcon: {
    marginLeft: 12,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#e2e8f0',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  timeRemaining: {
    fontSize: 16,
    fontWeight: '600',
    minWidth: 35,
    textAlign: 'right',
  },
});