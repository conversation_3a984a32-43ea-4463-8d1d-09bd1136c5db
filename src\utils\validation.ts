import { REGEX, LIMITS, PASSWORD_GENERATION, TOTP } from './constants';
import { PasswordValidationResult, PasswordPolicy } from '../types/auth';
import { NewVaultEntry, PasswordGeneratorOptions } from '../types/vault';

/**
 * Email validation
 */
export const validateEmail = (email: string): boolean => {
  return REGEX.EMAIL.test(email.trim());
};

/**
 * URL validation
 */
export const validateUrl = (url: string): boolean => {
  if (!url.trim()) return true; // URL is optional
  return REGEX.URL.test(url.trim());
};

/**
 * Password validation with policy enforcement
 */
export const validatePassword = (
  password: string,
  policy: PasswordPolicy
): PasswordValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  let strength = 0;

  // Length validation
  if (password.length < policy.minLength) {
    errors.push(`Password must be at least ${policy.minLength} characters long`);
  } else if (password.length >= policy.minLength) {
    strength += 1;
  }

  if (policy.maxLength && password.length > policy.maxLength) {
    errors.push(`Password must not exceed ${policy.maxLength} characters`);
  }

  // Character requirements
  if (policy.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else if (/[A-Z]/.test(password)) {
    strength += 1;
  }

  if (policy.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else if (/[a-z]/.test(password)) {
    strength += 1;
  }

  if (policy.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else if (/\d/.test(password)) {
    strength += 1;
  }

  if (policy.requireSymbols && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    strength += 1;
  }

  // Forbidden passwords
  if (policy.forbiddenPasswords?.includes(password.toLowerCase())) {
    errors.push('This password is too common and not allowed');
  }

  // Additional strength checks
  if (password.length >= 12) strength += 1;
  if (password.length >= 16) strength += 1;
  if (/[A-Z].*[A-Z]/.test(password)) strength += 1; // Multiple uppercase
  if (/\d.*\d/.test(password)) strength += 1; // Multiple numbers

  // Warnings for weak patterns
  if (/(.)\1{2,}/.test(password)) {
    warnings.push('Avoid repeating characters');
  }

  if (/123|abc|qwe/i.test(password)) {
    warnings.push('Avoid common sequences');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    strength: Math.min(strength, 5), // Cap at 5
  };
};

/**
 * TOTP secret validation
 */
export const validateTotpSecret = (secret: string): boolean => {
  const cleanSecret = secret.replace(/\s/g, '').toUpperCase();
  return REGEX.TOTP_SECRET.test(cleanSecret) && cleanSecret.length >= 16;
};

/**
 * Vault entry validation
 */
export const validateVaultEntry = (entry: NewVaultEntry): string[] => {
  const errors: string[] = [];

  // Title validation
  if (!entry.title.trim()) {
    errors.push('Title is required');
  } else if (entry.title.length > LIMITS.MAX_ENTRY_TITLE_LENGTH) {
    errors.push(`Title must not exceed ${LIMITS.MAX_ENTRY_TITLE_LENGTH} characters`);
  }

  // Type-specific validation
  switch (entry.type) {
    case 'password':
      if ('username' in entry && entry.username && entry.username.length > LIMITS.MAX_USERNAME_LENGTH) {
        errors.push(`Username must not exceed ${LIMITS.MAX_USERNAME_LENGTH} characters`);
      }
      
      if ('password' in entry && !entry.password) {
        errors.push('Password is required');
      } else if ('password' in entry && entry.password.length > LIMITS.MAX_PASSWORD_LENGTH) {
        errors.push(`Password must not exceed ${LIMITS.MAX_PASSWORD_LENGTH} characters`);
      }
      
      if ('url' in entry && entry.url && !validateUrl(entry.url)) {
        errors.push('Invalid URL format');
      } else if ('url' in entry && entry.url && entry.url.length > LIMITS.MAX_URL_LENGTH) {
        errors.push(`URL must not exceed ${LIMITS.MAX_URL_LENGTH} characters`);
      }
      
      if ('notes' in entry && entry.notes && entry.notes.length > LIMITS.MAX_NOTES_LENGTH) {
        errors.push(`Notes must not exceed ${LIMITS.MAX_NOTES_LENGTH} characters`);
      }
      break;

    case 'totp':
      if ('account' in entry && !entry.account.trim()) {
        errors.push('Account is required for TOTP entries');
      }
      
      if ('secret' in entry && !validateTotpSecret(entry.secret)) {
        errors.push('Invalid TOTP secret format');
      }
      
      if ('period' in entry && entry.period && (entry.period < TOTP.MIN_PERIOD || entry.period > TOTP.MAX_PERIOD)) {
        errors.push(`TOTP period must be between ${TOTP.MIN_PERIOD} and ${TOTP.MAX_PERIOD} seconds`);
      }
      
      if ('digits' in entry && entry.digits && (entry.digits < TOTP.MIN_DIGITS || entry.digits > TOTP.MAX_DIGITS)) {
        errors.push(`TOTP digits must be between ${TOTP.MIN_DIGITS} and ${TOTP.MAX_DIGITS}`);
      }
      break;

    case 'note':
      if ('content' in entry && !entry.content.trim()) {
        errors.push('Content is required for note entries');
      } else if ('content' in entry && entry.content.length > LIMITS.MAX_NOTES_LENGTH) {
        errors.push(`Content must not exceed ${LIMITS.MAX_NOTES_LENGTH} characters`);
      }
      break;
  }

  return errors;
};

/**
 * Password generator options validation
 */
export const validatePasswordGeneratorOptions = (options: PasswordGeneratorOptions): string[] => {
  const errors: string[] = [];

  if (options.length < PASSWORD_GENERATION.MIN_LENGTH) {
    errors.push(`Password length must be at least ${PASSWORD_GENERATION.MIN_LENGTH}`);
  }

  if (options.length > PASSWORD_GENERATION.MAX_LENGTH) {
    errors.push(`Password length must not exceed ${PASSWORD_GENERATION.MAX_LENGTH}`);
  }

  if (!options.includeUppercase && !options.includeLowercase && !options.includeNumbers && !options.includeSymbols) {
    errors.push('At least one character type must be selected');
  }

  return errors;
};

/**
 * File size validation
 */
export const validateFileSize = (size: number, maxSize: number = LIMITS.MAX_BACKUP_SIZE): boolean => {
  return size <= maxSize;
};

/**
 * Device ID validation
 */
export const validateDeviceId = (deviceId: string): boolean => {
  return deviceId.length >= 16 && /^[A-Za-z0-9]+$/.test(deviceId);
};

/**
 * Sanitize input string
 */
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

/**
 * Validate JSON structure
 */
export const validateJsonStructure = (jsonString: string, expectedKeys: string[]): boolean => {
  try {
    const parsed = JSON.parse(jsonString);
    return expectedKeys.every(key => key in parsed);
  } catch {
    return false;
  }
};

/**
 * Validate hex color
 */
export const validateHexColor = (color: string): boolean => {
  return REGEX.HEX_COLOR.test(color);
};

/**
 * Validate UUID
 */
export const validateUuid = (uuid: string): boolean => {
  return REGEX.UUID.test(uuid);
};

/**
 * Custom field validation
 */
export const validateCustomField = (name: string, value: string): string[] => {
  const errors: string[] = [];

  if (!name.trim()) {
    errors.push('Field name is required');
  } else if (name.length > LIMITS.MAX_FIELD_NAME_LENGTH) {
    errors.push(`Field name must not exceed ${LIMITS.MAX_FIELD_NAME_LENGTH} characters`);
  }

  if (value.length > LIMITS.MAX_FIELD_VALUE_LENGTH) {
    errors.push(`Field value must not exceed ${LIMITS.MAX_FIELD_VALUE_LENGTH} characters`);
  }

  return errors;
};

/**
 * Tag validation
 */
export const validateTag = (tag: string): boolean => {
  return tag.trim().length > 0 && 
         tag.length <= LIMITS.MAX_TAG_LENGTH && 
         /^[a-zA-Z0-9\s\-_]+$/.test(tag);
};

/**
 * Batch validation for multiple entries
 */
export const validateMultipleEntries = (entries: NewVaultEntry[]): { valid: NewVaultEntry[]; invalid: { entry: NewVaultEntry; errors: string[] }[] } => {
  const valid: NewVaultEntry[] = [];
  const invalid: { entry: NewVaultEntry; errors: string[] }[] = [];

  entries.forEach(entry => {
    const errors = validateVaultEntry(entry);
    if (errors.length === 0) {
      valid.push(entry);
    } else {
      invalid.push({ entry, errors });
    }
  });

  return { valid, invalid };
};