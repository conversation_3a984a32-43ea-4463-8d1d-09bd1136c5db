# Unlock and Sync Flow Architecture

## Overview

TWOFAI implements a secure, multi-modal unlock system with biometric and master password authentication, combined with intelligent sync conflict resolution. The system prioritizes security while maintaining a smooth user experience.

## Authentication Flows

### Initial Setup Flow
```mermaid
graph TD
    A[App Launch] --> B{First Time?}
    B -->|Yes| C[Create Master Password]
    C --> D[Derive Vault Key]
    D --> E[Create Empty Vault]
    E --> F{Enable Biometrics?}
    F -->|Yes| G[Generate Biometric Key]
    F -->|No| H[Save Encrypted Vault]
    G --> I[Encrypt Vault Key]
    I --> J[Store in Keychain]
    J --> H
    H --> K[Setup Complete]
```

### Biometric Unlock Flow
```mermaid
graph TD
    A[App Launch] --> B{Biometric Enabled?}
    B -->|Yes| C[Check Biometric Key]
    C --> D{Key Available?}
    D -->|Yes| E[Authenticate with Biometrics]
    D -->|No| F[Fallback to Master Password]
    E --> G{Auth Success?}
    G -->|Yes| H[Decrypt Vault Key]
    G -->|No| I{Retry Available?}
    I -->|Yes| E
    I -->|No| F
    H --> J[Load and Decrypt Vault]
    J --> K[Unlock Complete]
    F --> L[Master Password Flow]
```

### Master Password Flow
```mermaid
graph TD
    A[Master Password Input] --> B[Validate Input]
    B --> C[Load Salt]
    C --> D[Derive Key with Argon2id]
    D --> E[Attempt Vault Decryption]
    E --> F{Decryption Success?}
    F -->|Yes| G[Store Vault Key in Memory]
    F -->|No| H{Retry Count < Max?}
    H -->|Yes| I[Show Error, Allow Retry]
    H -->|No| J[Lock Account/Rate Limit]
    I --> A
    G --> K[Unlock Complete]
```

## Session Management

### Session State Machine
```typescript
enum SessionState {
  LOCKED = 'locked',
  UNLOCKING = 'unlocking',
  UNLOCKED = 'unlocked',
  LOCKING = 'locking',
  EXPIRED = 'expired'
}

interface SessionContext {
  state: SessionState;
  vaultKey: CryptoKey | null;
  lastActivity: number;
  lockTimeout: number;
  biometricEnabled: boolean;
}
```

### Auto-Lock Mechanisms
```typescript
class SessionManager {
  private lockTimer: NodeJS.Timeout | null = null;
  private readonly LOCK_REASONS = {
    TIMEOUT: 'timeout',
    BACKGROUND: 'background',
    MANUAL: 'manual',
    BIOMETRIC_CHANGE: 'biometric_change',
    SECURITY_EVENT: 'security_event'
  };

  startLockTimer(timeoutMs: number): void {
    this.clearLockTimer();
    this.lockTimer = setTimeout(() => {
      this.lockSession(this.LOCK_REASONS.TIMEOUT);
    }, timeoutMs);
  }

  onAppStateChange(state: 'active' | 'background' | 'inactive'): void {
    if (state === 'background' || state === 'inactive') {
      this.lockSession(this.LOCK_REASONS.BACKGROUND);
    }
  }
}
```

### Memory Cleanup
```typescript
const secureCleanup = (): void => {
  // Clear vault key from memory
  if (sessionContext.vaultKey) {
    // Note: WebCrypto keys are not directly clearable
    // They become eligible for garbage collection
    sessionContext.vaultKey = null;
  }

  // Clear decrypted vault data
  sessionContext.decryptedVault = null;

  // Clear any temporary decrypted strings
  clearTemporaryData();

  // Trigger garbage collection hint (where supported)
  if (global.gc) {
    global.gc();
  }
};
```

## Sync Architecture

### Sync State Management
```typescript
enum SyncState {
  IDLE = 'idle',
  SYNCING = 'syncing',
  CONFLICT = 'conflict',
  ERROR = 'error',
  OFFLINE = 'offline'
}

interface SyncContext {
  state: SyncState;
  lastSyncTime: number;
  pendingOperations: SyncOperation[];
  conflictResolution: ConflictResolution | null;
}
```

### Sync Operation Queue
```typescript
interface SyncOperation {
  id: string;
  type: 'upload' | 'download' | 'delete';
  timestamp: number;
  retryCount: number;
  vaultData?: EncryptedVault;
}

class SyncQueue {
  private queue: SyncOperation[] = [];
  private processing = false;

  async addOperation(operation: SyncOperation): Promise<void> {
    this.queue.push(operation);
    if (!this.processing) {
      await this.processQueue();
    }
  }

  private async processQueue(): Promise<void> {
    this.processing = true;
    while (this.queue.length > 0) {
      const operation = this.queue.shift()!;
      try {
        await this.executeOperation(operation);
      } catch (error) {
        await this.handleOperationError(operation, error);
      }
    }
    this.processing = false;
  }
}
```

### Conflict Resolution

#### Detection Algorithm
```typescript
interface ConflictDetection {
  localTimestamp: number;
  remoteTimestamp: number;
  localHash: string;
  remoteHash: string;
  conflictType: 'timestamp' | 'content' | 'both';
}

const detectConflict = (
  local: EncryptedVault,
  remote: EncryptedVault
): ConflictDetection | null => {
  const timestampConflict = Math.abs(local.timestamp - remote.timestamp) > CONFLICT_THRESHOLD;
  const contentConflict = local.dataHash !== remote.dataHash;
  
  if (timestampConflict || contentConflict) {
    return {
      localTimestamp: local.timestamp,
      remoteTimestamp: remote.timestamp,
      localHash: local.dataHash,
      remoteHash: remote.dataHash,
      conflictType: timestampConflict && contentConflict ? 'both' : 
                   timestampConflict ? 'timestamp' : 'content'
    };
  }
  
  return null;
};
```

#### Resolution Strategies
```typescript
enum ConflictResolutionStrategy {
  LAST_WRITE_WINS = 'last_write_wins',
  MANUAL_MERGE = 'manual_merge',
  LOCAL_PRIORITY = 'local_priority',
  REMOTE_PRIORITY = 'remote_priority'
}

class ConflictResolver {
  async resolveConflict(
    local: EncryptedVault,
    remote: EncryptedVault,
    strategy: ConflictResolutionStrategy
  ): Promise<EncryptedVault> {
    switch (strategy) {
      case ConflictResolutionStrategy.LAST_WRITE_WINS:
        return local.timestamp > remote.timestamp ? local : remote;
      
      case ConflictResolutionStrategy.MANUAL_MERGE:
        return await this.presentMergeUI(local, remote);
      
      case ConflictResolutionStrategy.LOCAL_PRIORITY:
        return local;
      
      case ConflictResolutionStrategy.REMOTE_PRIORITY:
        return remote;
    }
  }
}
```

### Offline-First Sync

#### Network Detection
```typescript
import NetInfo from '@react-native-community/netinfo';

class NetworkMonitor {
  private isOnline = false;
  private syncQueue: SyncQueue;

  constructor(syncQueue: SyncQueue) {
    this.syncQueue = syncQueue;
    this.setupNetworkListener();
  }

  private setupNetworkListener(): void {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      if (wasOffline && this.isOnline) {
        // Connection restored - process pending operations
        this.syncQueue.resumeProcessing();
      }
    });
  }
}
```

#### Offline Operation Queuing
```typescript
class OfflineSync {
  private pendingOperations: Map<string, SyncOperation> = new Map();

  async queueOperation(operation: SyncOperation): Promise<void> {
    this.pendingOperations.set(operation.id, operation);
    await this.persistQueueToDisk();
  }

  async resumeOnline(): Promise<void> {
    const operations = Array.from(this.pendingOperations.values());
    for (const operation of operations) {
      try {
        await this.executeOperation(operation);
        this.pendingOperations.delete(operation.id);
      } catch (error) {
        operation.retryCount++;
        if (operation.retryCount >= MAX_RETRIES) {
          this.pendingOperations.delete(operation.id);
        }
      }
    }
    await this.persistQueueToDisk();
  }
}
```

## Error Handling and Recovery

### Authentication Errors
```typescript
class AuthErrorHandler {
  handleBiometricError(error: BiometricError): AuthRecoveryAction {
    switch (error.code) {
      case 'UserCancel':
        return { action: 'fallback_to_password', showMessage: false };
      
      case 'BiometryNotAvailable':
        return { action: 'disable_biometric', showMessage: true };
      
      case 'BiometryNotEnrolled':
        return { action: 'prompt_biometric_setup', showMessage: true };
      
      case 'BiometryLockout':
        return { action: 'temporary_lockout', duration: 30000 };
      
      default:
        return { action: 'fallback_to_password', showMessage: true };
    }
  }

  handlePasswordError(error: PasswordError): AuthRecoveryAction {
    switch (error.code) {
      case 'InvalidPassword':
        return { action: 'retry_with_hint', attemptsRemaining: error.attemptsLeft };
      
      case 'TooManyAttempts':
        return { action: 'rate_limit', duration: this.calculateBackoff(error.attempts) };
      
      case 'VaultCorrupted':
        return { action: 'vault_recovery', requiresBackup: true };
      
      default:
        return { action: 'show_error', message: error.message };
    }
  }
}
```

### Sync Error Recovery
```typescript
class SyncErrorHandler {
  async handleSyncError(error: SyncError): Promise<SyncRecoveryAction> {
    switch (error.type) {
      case 'NetworkError':
        return { action: 'queue_for_retry', delay: this.getRetryDelay(error.retryCount) };
      
      case 'AuthenticationError':
        return { action: 'reauthenticate', clearTokens: true };
      
      case 'ConflictError':
        return { action: 'resolve_conflict', strategy: 'manual' };
      
      case 'ServerError':
        return { action: 'exponential_backoff', baseDelay: 1000 };
      
      case 'QuotaExceeded':
        return { action: 'notify_user', requiresAction: true };
      
      default:
        return { action: 'log_and_continue', severity: 'warning' };
    }
  }
}
```

## Security Measures

### Rate Limiting
```typescript
class RateLimiter {
  private attempts: Map<string, number> = new Map();
  private lockouts: Map<string, number> = new Map();

  isAllowed(identifier: string): boolean {
    const lockoutTime = this.lockouts.get(identifier);
    if (lockoutTime && Date.now() < lockoutTime) {
      return false;
    }

    const attempts = this.attempts.get(identifier) || 0;
    return attempts < MAX_ATTEMPTS;
  }

  recordAttempt(identifier: string, success: boolean): void {
    if (success) {
      this.attempts.delete(identifier);
      this.lockouts.delete(identifier);
    } else {
      const attempts = (this.attempts.get(identifier) || 0) + 1;
      this.attempts.set(identifier, attempts);
      
      if (attempts >= MAX_ATTEMPTS) {
        const lockoutDuration = this.calculateLockoutDuration(attempts);
        this.lockouts.set(identifier, Date.now() + lockoutDuration);
      }
    }
  }
}
```

### Session Validation
```typescript
class SessionValidator {
  validateSession(context: SessionContext): ValidationResult {
    // Check session timeout
    if (Date.now() - context.lastActivity > context.lockTimeout) {
      return { valid: false, reason: 'timeout' };
    }

    // Validate vault key presence
    if (!context.vaultKey) {
      return { valid: false, reason: 'missing_key' };
    }

    // Check for security events
    if (this.hasSecurityEvent()) {
      return { valid: false, reason: 'security_event' };
    }

    return { valid: true };
  }

  private hasSecurityEvent(): boolean {
    // Check for jailbreak/root detection
    // Check for debugger attachment
    // Check for biometric enrollment changes
    // Check for device passcode changes
    return false; // Implement platform-specific checks
  }
}
```

## Performance Optimization

### Lazy Loading
```typescript
class VaultManager {
  private cache: Map<string, DecryptedEntry> = new Map();

  async getEntry(id: string): Promise<DecryptedEntry> {
    // Check cache first
    if (this.cache.has(id)) {
      return this.cache.get(id)!;
    }

    // Load and decrypt on demand
    const entry = await this.loadAndDecryptEntry(id);
    this.cache.set(id, entry);
    return entry;
  }

  preloadCriticalEntries(): Promise<void> {
    // Preload frequently accessed entries
    // Use priority queue for loading order
    // Implement background loading
  }
}
```

### Batch Operations
```typescript
class BatchProcessor {
  private batchQueue: VaultOperation[] = [];
  private batchTimer: NodeJS.Timeout | null = null;

  addOperation(operation: VaultOperation): void {
    this.batchQueue.push(operation);
    
    if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => {
        this.processBatch();
      }, BATCH_DELAY);
    }
  }

  private async processBatch(): Promise<void> {
    const operations = [...this.batchQueue];
    this.batchQueue.length = 0;
    this.batchTimer = null;

    // Group operations by type
    const batches = this.groupOperations(operations);
    
    // Process each batch
    for (const batch of batches) {
      await this.executeBatch(batch);
    }
  }
}
```

## Monitoring and Analytics

### Performance Metrics
```typescript
interface PerformanceMetrics {
  unlockTime: number;
  syncDuration: number;
  conflictResolutionTime: number;
  cacheHitRate: number;
  errorRate: number;
}

class MetricsCollector {
  collectUnlockMetrics(startTime: number, endTime: number, method: string): void {
    const duration = endTime - startTime;
    this.recordMetric('unlock_duration', duration, { method });
  }

  collectSyncMetrics(operation: SyncOperation, duration: number, success: boolean): void {
    this.recordMetric('sync_duration', duration, { 
      operation: operation.type, 
      success: success.toString() 
    });
  }
}
```

### Error Tracking
```typescript
class ErrorTracker {
  trackAuthError(error: AuthError, context: AuthContext): void {
    this.logError('auth_error', {
      error: error.code,
      method: context.method,
      retryCount: context.retryCount,
      platform: Platform.OS
    });
  }

  trackSyncError(error: SyncError, operation: SyncOperation): void {
    this.logError('sync_error', {
      error: error.type,
      operation: operation.type,
      retryCount: operation.retryCount,
      networkState: this.getNetworkState()
    });
  }
}
```