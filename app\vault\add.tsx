import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Key, Shield, FileText, Eye, EyeOff, RefreshCw } from 'lucide-react-native';
import { router } from 'expo-router';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';

type EntryType = 'password' | 'totp' | 'note';

export default function AddEntryScreen() {
  const [entryType, setEntryType] = useState<EntryType>('password');
  const [title, setTitle] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [url, setUrl] = useState('');
  const [notes, setNotes] = useState('');
  const [totpSecret, setTotpSecret] = useState('');
  const [issuer, setIssuer] = useState('');
  const [account, setAccount] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const generatePassword = () => {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';
    for (let i = 0; i < 16; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    setPassword(result);
  };

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a title');
      return;
    }

    if (entryType === 'password' && !password.trim()) {
      Alert.alert('Error', 'Please enter a password');
      return;
    }

    if (entryType === 'totp' && (!totpSecret.trim() || !account.trim())) {
      Alert.alert('Error', 'Please enter TOTP secret and account');
      return;
    }

    if (entryType === 'note' && !notes.trim()) {
      Alert.alert('Error', 'Please enter note content');
      return;
    }

    setIsLoading(true);

    // Simulate saving
    setTimeout(() => {
      setIsLoading(false);
      Alert.alert(
        'Success',
        'Entry saved successfully!',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    }, 1000);
  };

  const renderEntryTypeSelector = () => (
    <View style={styles.typeSelector}>
      <Text style={styles.sectionTitle}>Entry Type</Text>
      <View style={styles.typeButtons}>
        <TouchableOpacity
          style={[styles.typeButton, entryType === 'password' && styles.typeButtonActive]}
          onPress={() => setEntryType('password')}
        >
          <Key size={20} color={entryType === 'password' ? '#3b82f6' : '#64748b'} />
          <Text style={[styles.typeButtonText, entryType === 'password' && styles.typeButtonTextActive]}>
            Password
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.typeButton, entryType === 'totp' && styles.typeButtonActive]}
          onPress={() => setEntryType('totp')}
        >
          <Shield size={20} color={entryType === 'totp' ? '#3b82f6' : '#64748b'} />
          <Text style={[styles.typeButtonText, entryType === 'totp' && styles.typeButtonTextActive]}>
            2FA
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.typeButton, entryType === 'note' && styles.typeButtonActive]}
          onPress={() => setEntryType('note')}
        >
          <FileText size={20} color={entryType === 'note' ? '#3b82f6' : '#64748b'} />
          <Text style={[styles.typeButtonText, entryType === 'note' && styles.typeButtonTextActive]}>
            Note
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderPasswordForm = () => (
    <View style={styles.formSection}>
      <Input
        label="Title"
        placeholder="e.g., Gmail Account"
        value={title}
        onChangeText={setTitle}
      />

      <Input
        label="Username/Email"
        placeholder="Enter username or email"
        value={username}
        onChangeText={setUsername}
        autoCapitalize="none"
        keyboardType="email-address"
      />

      <View style={styles.passwordInputContainer}>
        <Input
          label="Password"
          placeholder="Enter password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry={!showPassword}
          rightIcon={showPassword ? <EyeOff size={20} color="#6b7280" /> : <Eye size={20} color="#6b7280" />}
          onRightIconPress={() => setShowPassword(!showPassword)}
          autoCapitalize="none"
        />
        <TouchableOpacity style={styles.generateButton} onPress={generatePassword}>
          <RefreshCw size={16} color="#3b82f6" />
          <Text style={styles.generateButtonText}>Generate</Text>
        </TouchableOpacity>
      </View>

      <Input
        label="Website URL"
        placeholder="https://example.com"
        value={url}
        onChangeText={setUrl}
        autoCapitalize="none"
        keyboardType="url"
      />

      <Input
        label="Notes (Optional)"
        placeholder="Additional notes..."
        value={notes}
        onChangeText={setNotes}
        multiline
        numberOfLines={3}
      />
    </View>
  );

  const renderTOTPForm = () => (
    <View style={styles.formSection}>
      <Input
        label="Title"
        placeholder="e.g., Google Account"
        value={title}
        onChangeText={setTitle}
      />

      <Input
        label="Issuer"
        placeholder="e.g., Google, GitHub"
        value={issuer}
        onChangeText={setIssuer}
      />

      <Input
        label="Account"
        placeholder="e.g., <EMAIL>"
        value={account}
        onChangeText={setAccount}
        autoCapitalize="none"
        keyboardType="email-address"
      />

      <Input
        label="Secret Key"
        placeholder="Enter TOTP secret key"
        value={totpSecret}
        onChangeText={setTotpSecret}
        autoCapitalize="characters"
      />

      <View style={styles.totpInfo}>
        <Text style={styles.totpInfoText}>
          💡 You can find the secret key when setting up 2FA, or scan a QR code to extract it.
        </Text>
      </View>
    </View>
  );

  const renderNoteForm = () => (
    <View style={styles.formSection}>
      <Input
        label="Title"
        placeholder="e.g., WiFi Password"
        value={title}
        onChangeText={setTitle}
      />

      <Input
        label="Content"
        placeholder="Enter your note content..."
        value={notes}
        onChangeText={setNotes}
        multiline
        numberOfLines={8}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#1e293b" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Add Entry</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderEntryTypeSelector()}
        
        {entryType === 'password' && renderPasswordForm()}
        {entryType === 'totp' && renderTOTPForm()}
        {entryType === 'note' && renderNoteForm()}

        <View style={styles.buttonContainer}>
          <Button
            onPress={handleSave}
            loading={isLoading}
            disabled={isLoading}
            fullWidth
          >
            Save Entry
          </Button>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#f8fafc',
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: '600',
    color: '#1e293b',
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  typeSelector: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
  },
  typeButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 16,
    paddingHorizontal: 12,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  typeButtonActive: {
    backgroundColor: '#eff6ff',
    borderColor: '#3b82f6',
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748b',
  },
  typeButtonTextActive: {
    color: '#3b82f6',
  },
  formSection: {
    gap: 20,
    marginBottom: 32,
  },
  passwordInputContainer: {
    position: 'relative',
  },
  generateButton: {
    position: 'absolute',
    right: 12,
    top: 32,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#dbeafe',
  },
  generateButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#3b82f6',
  },
  totpInfo: {
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#bae6fd',
  },
  totpInfoText: {
    fontSize: 14,
    color: '#0369a1',
    lineHeight: 20,
  },
  buttonContainer: {
    paddingBottom: 32,
  },
});