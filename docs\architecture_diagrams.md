# Architecture Diagrams

## Overview

This document provides visual representations of TWOFAI's architecture, including system components, data flow, security boundaries, and operational processes. These diagrams serve as a reference for understanding the system's structure and behavior.

## System Architecture Overview

```mermaid
graph TB
    subgraph "Client Applications"
        Mobile[📱 Mobile App<br/>React Native]
        Web[🌐 Web App<br/>React]
        Extension[🔌 Browser Extension<br/>WebExtension API]
        Desktop[💻 Desktop App<br/>Electron]
    end

    subgraph "Shared Core"
        CoreLib[@twofai/core<br/>Crypto, Vault, Types]
    end

    subgraph "Cloud Services"
        Supabase[☁️ Supabase<br/>Auth, Database, Storage]
        API[🔗 API Layer<br/>Sync, Conflict Resolution]
    end

    subgraph "Platform Services"
        iOS[🍎 iOS Services<br/>Keychain, Biometrics]
        Android[🤖 Android Services<br/>Keystore, Biometrics]
        WebAPIs[🌐 Web APIs<br/>WebCrypto, IndexedDB]
    end

    Mobile --> CoreLib
    Web --> CoreLib
    Extension --> CoreLib
    Desktop --> CoreLib

    Mobile --> iOS
    Mobile --> Android
    Web --> WebAPIs
    Desktop --> WebAPIs

    Mobile -.->|Encrypted Sync| Supabase
    Web -.->|Encrypted Sync| Supabase
    Extension -.->|Encrypted Sync| Supabase
    Desktop -.->|Encrypted Sync| Supabase

    Supabase --> API
```

## Mobile Application Architecture

```mermaid
graph TB
    subgraph "Presentation Layer"
        Screens[📱 Screens<br/>React Native Components]
        Navigation[🧭 Navigation<br/>Expo Router]
        UI[🎨 UI Components<br/>Design System]
    end

    subgraph "State Management"
        Hooks[🪝 Custom Hooks<br/>useVault, useAuth, useSync]
        Context[🏪 React Context<br/>Global State]
        LocalState[📊 Component State<br/>Local UI State]
    end

    subgraph "Business Logic"
        Services[⚙️ Services<br/>Vault, Auth, Crypto, Sync]
        Managers[📋 Managers<br/>Session, Storage, Network]
        Utils[🔧 Utilities<br/>Validation, Formatting]
    end

    subgraph "Data Layer"
        Storage[💾 Platform Storage<br/>SecureStore, FileSystem]
        Cache[⚡ Memory Cache<br/>Encrypted Data]
        Network[🌐 Network Layer<br/>Supabase Client]
    end

    subgraph "Platform Integration"
        Biometrics[👤 Biometric Auth<br/>Face ID, Touch ID]
        Keychain[🔐 Secure Storage<br/>iOS Keychain, Android Keystore]
        Notifications[📢 Push Notifications<br/>Sync Status]
    end

    Screens --> Navigation
    Screens --> UI
    Screens --> Hooks
    Hooks --> Context
    Hooks --> Services
    Context --> LocalState
    Services --> Managers
    Services --> Utils
    Managers --> Storage
    Managers --> Cache
    Managers --> Network
    Storage --> Biometrics
    Storage --> Keychain
    Network --> Notifications
```

## Vault Encryption Flow

```mermaid
flowchart TD
    Start([User Enters Master Password]) --> ValidateInput{Valid Input?}
    
    ValidateInput -->|No| ShowError[Show Error Message]
    ShowError --> Start
    
    ValidateInput -->|Yes| LoadSalt[Load Salt from Storage]
    LoadSalt --> DeriveKey[Derive Vault Key<br/>Argon2id]
    
    DeriveKey --> LoadEncryptedVault[Load Encrypted Vault<br/>from Storage]
    LoadEncryptedVault --> DecryptVault{Decrypt Vault<br/>AES-256-GCM}
    
    DecryptVault -->|Success| StoreInMemory[Store Decrypted Vault<br/>in Memory]
    DecryptVault -->|Failure| HandleError{Authentication<br/>Attempts < Max?}
    
    HandleError -->|Yes| ShowError
    HandleError -->|No| LockAccount[Lock Account<br/>Rate Limit]
    
    StoreInMemory --> StartSession[Start Authenticated Session]
    StartSession --> SetupAutoLock[Setup Auto-Lock Timer]
    SetupAutoLock --> VaultReady([Vault Ready for Use])

    subgraph "Security Boundaries"
        DeriveKey
        DecryptVault
        StoreInMemory
    end

    subgraph "Memory Protection"
        ClearOnLock[Clear Memory on Lock]
        ClearOnBackground[Clear on App Background]
        ClearOnTimeout[Clear on Session Timeout]
    end

    VaultReady -.-> ClearOnLock
    VaultReady -.-> ClearOnBackground
    VaultReady -.-> ClearOnTimeout
```

## Biometric Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant B as Biometric Service
    participant K as Keychain/Keystore
    participant V as Vault Service

    U->>A: Launch App
    A->>A: Check if Biometric Enabled
    
    alt Biometric Enabled
        A->>B: Check Biometric Availability
        B-->>A: Biometric Available
        
        A->>U: Show Biometric Prompt
        U->>B: Provide Biometric (Face/Finger)
        
        alt Biometric Success
            B->>K: Request Encrypted Vault Key
            K-->>B: Return Encrypted Key
            B->>B: Decrypt Key with Biometric
            B-->>A: Return Decrypted Vault Key
            
            A->>V: Decrypt Vault with Key
            V-->>A: Return Decrypted Vault
            A->>U: Show Vault Interface
            
        else Biometric Failure
            B-->>A: Biometric Failed
            A->>U: Show Fallback Options
            
            alt User Chooses Password
                A->>U: Show Password Input
                U->>A: Enter Master Password
                A->>V: Authenticate with Password
                V-->>A: Authentication Result
            else User Cancels
                A->>A: Stay on Lock Screen
            end
        end
        
    else Biometric Not Enabled
        A->>U: Show Password Input
        U->>A: Enter Master Password
        A->>V: Authenticate with Password
        V-->>A: Authentication Result
    end
```

## Sync Conflict Resolution Flow

```mermaid
flowchart TD
    SyncRequest[Sync Request Initiated] --> CheckNetwork{Network Available?}
    
    CheckNetwork -->|No| QueueOffline[Queue for Later<br/>Store in Offline Queue]
    QueueOffline --> End([End])
    
    CheckNetwork -->|Yes| FetchRemote[Fetch Remote Vault<br/>with Timestamps]
    FetchRemote --> CompareTimestamps{Compare<br/>Timestamps}
    
    CompareTimestamps -->|Local Newer| UploadLocal[Upload Local Vault<br/>to Remote]
    UploadLocal --> UpdateSyncTime[Update Last Sync Time]
    UpdateSyncTime --> End
    
    CompareTimestamps -->|Remote Newer| DownloadRemote[Download Remote Vault]
    DownloadRemote --> MergeLocal[Merge with Local Changes]
    MergeLocal --> UpdateSyncTime
    
    CompareTimestamps -->|Conflict| DetectConflictType{Conflict Type?}
    
    DetectConflictType -->|Timestamp Only| LastWriteWins[Apply Last Write Wins]
    LastWriteWins --> ResolveConflict[Resolve Conflict]
    
    DetectConflictType -->|Content Diff| ShowConflictUI[Show Conflict Resolution UI]
    ShowConflictUI --> UserChoice{User Choice?}
    
    UserChoice -->|Keep Local| UseLocal[Use Local Version]
    UserChoice -->|Keep Remote| UseRemote[Use Remote Version]
    UserChoice -->|Manual Merge| ManualMerge[Manual Merge Interface]
    
    UseLocal --> ResolveConflict
    UseRemote --> ResolveConflict
    ManualMerge --> ResolveConflict
    
    ResolveConflict --> SaveResolution[Save Resolved Vault]
    SaveResolution --> LogConflict[Log Conflict Resolution]
    LogConflict --> UpdateSyncTime
    
    subgraph "Conflict Resolution Strategies"
        LastWriteWins
        ShowConflictUI
        ManualMerge
    end
    
    subgraph "Offline Handling"
        QueueOffline
        OfflineResume[Resume on Connection]
    end
```

## Data Storage Architecture

```mermaid
graph TB
    subgraph "Application Layer"
        VaultService[Vault Service]
        AuthService[Auth Service]
        SyncService[Sync Service]
    end

    subgraph "Storage Abstraction"
        StorageInterface[Platform Storage Interface]
    end

    subgraph "iOS Storage"
        iOSKeychain[iOS Keychain<br/>Biometric Keys]
        iOSFiles[iOS File System<br/>Encrypted Vault]
        iOSPrefs[iOS UserDefaults<br/>Preferences]
    end

    subgraph "Android Storage"
        AndroidKeystore[Android Keystore<br/>Biometric Keys]
        AndroidFiles[Android Internal Storage<br/>Encrypted Vault]
        AndroidPrefs[Android SharedPreferences<br/>Preferences]
    end

    subgraph "Web Storage"
        IndexedDB[IndexedDB<br/>Encrypted Vault]
        LocalStorage[LocalStorage<br/>Preferences]
        WebCrypto[Web Crypto API<br/>Key Operations]
    end

    subgraph "Remote Storage"
        SupabaseDB[(Supabase Database<br/>Encrypted Vault Blobs)]
        SupabaseAuth[Supabase Auth<br/>User Sessions]
    end

    VaultService --> StorageInterface
    AuthService --> StorageInterface
    SyncService --> StorageInterface

    StorageInterface --> iOSKeychain
    StorageInterface --> iOSFiles
    StorageInterface --> iOSPrefs

    StorageInterface --> AndroidKeystore
    StorageInterface --> AndroidFiles
    StorageInterface --> AndroidPrefs

    StorageInterface --> IndexedDB
    StorageInterface --> LocalStorage
    StorageInterface --> WebCrypto

    SyncService -.->|Encrypted Data Only| SupabaseDB
    AuthService -.->|Authentication| SupabaseAuth

    classDef storage fill:#e1f5fe
    classDef security fill:#fff3e0
    classDef remote fill:#f3e5f5

    class iOSKeychain,AndroidKeystore,WebCrypto security
    class iOSFiles,AndroidFiles,IndexedDB,iOSPrefs,AndroidPrefs,LocalStorage storage
    class SupabaseDB,SupabaseAuth remote
```

## Security Boundaries

```mermaid
graph TB
    subgraph "Trusted Execution Environment"
        SecureEnclave[Secure Enclave<br/>iOS]
        TEE[Trusted Execution Environment<br/>Android]
        HSM[Hardware Security Module<br/>Enterprise]
    end

    subgraph "OS Security Boundary"
        Keychain[System Keychain<br/>Key Storage]
        Sandbox[App Sandbox<br/>Process Isolation]
        Permissions[OS Permissions<br/>Access Control]
    end

    subgraph "Application Security Boundary"
        CryptoLayer[Cryptographic Layer<br/>AES-256-GCM, Argon2id]
        MemoryProtection[Memory Protection<br/>Secure Cleanup]
        InputValidation[Input Validation<br/>Data Sanitization]
    end

    subgraph "Network Security Boundary"
        TLS[TLS 1.3<br/>Transport Encryption]
        CertPinning[Certificate Pinning<br/>MITM Protection]
        EndToEndEncryption[End-to-End Encryption<br/>Client-Side Only]
    end

    subgraph "User Interface Boundary"
        ScreenProtection[Screen Protection<br/>Anti-Screenshot]
        AutoLock[Auto-Lock<br/>Session Timeout]
        BiometricPrompt[Biometric Prompt<br/>Secure Authentication]
    end

    SecureEnclave --> Keychain
    TEE --> Keychain
    HSM --> Keychain

    Keychain --> CryptoLayer
    Sandbox --> MemoryProtection
    Permissions --> InputValidation

    CryptoLayer --> TLS
    MemoryProtection --> CertPinning
    InputValidation --> EndToEndEncryption

    TLS --> ScreenProtection
    CertPinning --> AutoLock
    EndToEndEncryption --> BiometricPrompt

    classDef hardware fill:#ffcdd2
    classDef system fill:#fff9c4
    classDef app fill:#dcedc8
    classDef network fill:#bbdefb
    classDef ui fill:#f3e5f5

    class SecureEnclave,TEE,HSM hardware
    class Keychain,Sandbox,Permissions system
    class CryptoLayer,MemoryProtection,InputValidation app
    class TLS,CertPinning,EndToEndEncryption network
    class ScreenProtection,AutoLock,BiometricPrompt ui
```

## Component Interaction Diagram

```mermaid
graph TB
    subgraph "UI Layer"
        VaultScreen[Vault Screen]
        UnlockScreen[Unlock Screen]
        SettingsScreen[Settings Screen]
        EntryForm[Entry Form]
    end

    subgraph "State Management"
        VaultContext[Vault Context]
        AuthContext[Auth Context]
        SyncContext[Sync Context]
    end

    subgraph "Custom Hooks"
        useVault[useVault]
        useAuth[useAuth]
        useSync[useSync]
        useBiometric[useBiometric]
    end

    subgraph "Services"
        VaultManager[Vault Manager]
        AuthManager[Auth Manager]
        SyncManager[Sync Manager]
        CryptoService[Crypto Service]
    end

    subgraph "Storage"
        PlatformStorage[Platform Storage]
        MemoryCache[Memory Cache]
    end

    VaultScreen --> VaultContext
    UnlockScreen --> AuthContext
    SettingsScreen --> SyncContext
    EntryForm --> VaultContext

    VaultContext --> useVault
    AuthContext --> useAuth
    SyncContext --> useSync

    useVault --> VaultManager
    useAuth --> AuthManager
    useAuth --> useBiometric
    useSync --> SyncManager

    VaultManager --> CryptoService
    AuthManager --> CryptoService
    SyncManager --> CryptoService

    VaultManager --> PlatformStorage
    AuthManager --> PlatformStorage
    SyncManager --> PlatformStorage

    VaultManager --> MemoryCache
    AuthManager --> MemoryCache

    classDef ui fill:#e3f2fd
    classDef state fill:#fff3e0
    classDef hooks fill:#e8f5e8
    classDef services fill:#fce4ec
    classDef storage fill:#f3e5f5

    class VaultScreen,UnlockScreen,SettingsScreen,EntryForm ui
    class VaultContext,AuthContext,SyncContext state
    class useVault,useAuth,useSync,useBiometric hooks
    class VaultManager,AuthManager,SyncManager,CryptoService services
    class PlatformStorage,MemoryCache storage
```

## Deployment Architecture

```mermaid
graph TB
    subgraph "Development Environment"
        DevMachine[👨‍💻 Developer Machine]
        LocalTesting[🧪 Local Testing]
        Simulator[📱 iOS/Android Simulator]
    end

    subgraph "CI/CD Pipeline"
        GitRepo[📁 Git Repository]
        BuildServer[🏗️ Build Server]
        TestRunner[🧪 Test Runner]
        SecurityScan[🔍 Security Scanner]
    end

    subgraph "App Stores"
        AppStore[🍎 Apple App Store]
        PlayStore[🤖 Google Play Store]
        WebDeploy[🌐 Web Deployment]
    end

    subgraph "Backend Services"
        Supabase[☁️ Supabase]
        CDN[🌐 CDN]
        Monitoring[📊 Monitoring]
    end

    subgraph "User Devices"
        iOSDevice[📱 iOS Device]
        AndroidDevice[📱 Android Device]
        WebBrowser[🌐 Web Browser]
    end

    DevMachine --> GitRepo
    LocalTesting --> Simulator
    
    GitRepo --> BuildServer
    BuildServer --> TestRunner
    TestRunner --> SecurityScan
    SecurityScan --> AppStore
    SecurityScan --> PlayStore
    SecurityScan --> WebDeploy

    AppStore --> iOSDevice
    PlayStore --> AndroidDevice
    WebDeploy --> WebBrowser

    iOSDevice -.->|Encrypted Sync| Supabase
    AndroidDevice -.->|Encrypted Sync| Supabase
    WebBrowser -.->|Encrypted Sync| Supabase

    Supabase --> CDN
    Supabase --> Monitoring

    classDef dev fill:#e8f5e8
    classDef cicd fill:#fff3e0
    classDef store fill:#e3f2fd
    classDef backend fill:#fce4ec
    classDef device fill:#f3e5f5

    class DevMachine,LocalTesting,Simulator dev
    class GitRepo,BuildServer,TestRunner,SecurityScan cicd
    class AppStore,PlayStore,WebDeploy store
    class Supabase,CDN,Monitoring backend
    class iOSDevice,AndroidDevice,WebBrowser device
```

## Network Communication Flow

```mermaid
sequenceDiagram
    participant C as Client App
    participant N as Network Layer
    participant L as Load Balancer
    participant A as API Gateway
    participant S as Supabase
    participant D as Database

    Note over C,D: Sync Operation Flow

    C->>N: Initiate Sync Request
    N->>N: Check Network Status
    N->>L: HTTPS Request (TLS 1.3)
    L->>A: Route to API Gateway
    A->>A: Validate API Key
    A->>S: Forward to Supabase
    S->>S: Authenticate User
    S->>D: Query Vault Data
    D-->>S: Return Encrypted Vault
    S-->>A: Response with Data
    A-->>L: Forward Response
    L-->>N: HTTPS Response
    N-->>C: Return Data

    Note over C,D: Error Handling

    alt Network Error
        N->>C: Queue for Retry
        C->>C: Store in Offline Queue
    else Authentication Error
        A-->>C: 401 Unauthorized
        C->>C: Trigger Re-authentication
    else Server Error
        S-->>C: 500 Server Error
        C->>C: Exponential Backoff
    end

    Note over C,D: Conflict Resolution

    alt Conflict Detected
        S-->>C: Conflict Response
        C->>C: Show Resolution UI
        C->>S: Resolution Choice
        S->>D: Update with Resolution
    end
```

## Memory Management Flow

```mermaid
flowchart TD
    AppLaunch[App Launch] --> InitializeMemory[Initialize Memory Manager]
    InitializeMemory --> UserAuth{User Authentication}
    
    UserAuth -->|Success| LoadVault[Load Encrypted Vault]
    UserAuth -->|Failure| SecureCleanup[Secure Memory Cleanup]
    
    LoadVault --> DecryptVault[Decrypt Vault in Memory]
    DecryptVault --> MarkSensitive[Mark Data as Sensitive]
    MarkSensitive --> ActiveSession[Active Session]
    
    ActiveSession --> MonitorActivity[Monitor User Activity]
    MonitorActivity --> CheckTimeout{Session Timeout?}
    
    CheckTimeout -->|No| MonitorActivity
    CheckTimeout -->|Yes| InitiateLock[Initiate Lock Sequence]
    
    ActiveSession --> AppBackground{App Backgrounded?}
    AppBackground -->|Yes| InitiateLock
    AppBackground -->|No| MonitorActivity
    
    ActiveSession --> ManualLock{Manual Lock?}
    ManualLock -->|Yes| InitiateLock
    ManualLock -->|No| MonitorActivity
    
    InitiateLock --> ClearSensitiveData[Clear Sensitive Data]
    ClearSensitiveData --> ZeroMemory[Zero Memory Locations]
    ZeroMemory --> ForceGC[Force Garbage Collection]
    ForceGC --> SecureCleanup
    
    SecureCleanup --> LockedState[Locked State]
    LockedState --> UserAuth

    subgraph "Memory Protection"
        MarkSensitive
        ClearSensitiveData
        ZeroMemory
        ForceGC
    end

    subgraph "Cleanup Triggers"
        CheckTimeout
        AppBackground
        ManualLock
    end

    style MarkSensitive fill:#ffcdd2
    style ClearSensitiveData fill:#ffcdd2
    style ZeroMemory fill:#ffcdd2
    style ForceGC fill:#ffcdd2
```

## Error Handling Architecture

```mermaid
graph TB
    subgraph "Error Sources"
        CryptoError[Cryptographic Errors]
        NetworkError[Network Errors]
        StorageError[Storage Errors]
        AuthError[Authentication Errors]
        ValidationError[Validation Errors]
    end

    subgraph "Error Processing"
        ErrorBoundary[React Error Boundary]
        ErrorHandler[Global Error Handler]
        ErrorLogger[Error Logger]
        ErrorClassifier[Error Classifier]
    end

    subgraph "Error Recovery"
        RetryMechanism[Retry Mechanism]
        FallbackActions[Fallback Actions]
        UserNotification[User Notification]
        GracefulDegradation[Graceful Degradation]
    end

    subgraph "Error Reporting"
        LocalLogs[Local Error Logs]
        CrashReporting[Crash Reporting]
        SecurityAudit[Security Audit Log]
        UserFeedback[User Feedback]
    end

    CryptoError --> ErrorBoundary
    NetworkError --> ErrorHandler
    StorageError --> ErrorHandler
    AuthError --> ErrorHandler
    ValidationError --> ErrorBoundary

    ErrorBoundary --> ErrorLogger
    ErrorHandler --> ErrorLogger
    ErrorLogger --> ErrorClassifier

    ErrorClassifier --> RetryMechanism
    ErrorClassifier --> FallbackActions
    ErrorClassifier --> UserNotification
    ErrorClassifier --> GracefulDegradation

    RetryMechanism --> LocalLogs
    FallbackActions --> CrashReporting
    UserNotification --> SecurityAudit
    GracefulDegradation --> UserFeedback

    classDef error fill:#ffcdd2
    classDef processing fill:#fff3e0
    classDef recovery fill:#e8f5e8
    classDef reporting fill:#e3f2fd

    class CryptoError,NetworkError,StorageError,AuthError,ValidationError error
    class ErrorBoundary,ErrorHandler,ErrorLogger,ErrorClassifier processing
    class RetryMechanism,FallbackActions,UserNotification,GracefulDegradation recovery
    class LocalLogs,CrashReporting,SecurityAudit,UserFeedback reporting
```

These architecture diagrams provide a comprehensive visual reference for understanding TWOFAI's structure, data flow, security boundaries, and operational processes. They serve as a foundation for development, security audits, and system maintenance activities.