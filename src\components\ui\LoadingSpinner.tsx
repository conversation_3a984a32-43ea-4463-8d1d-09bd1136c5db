import React from 'react';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { LoadingSpinnerProps } from '../../types/ui';

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = '#3b82f6',
  text,
}) => {
  const getSize = () => {
    switch (size) {
      case 'sm':
        return 'small';
      case 'lg':
        return 'large';
      default:
        return 'small';
    }
  };

  return (
    <View style={styles.container}>
      <ActivityIndicator size={getSize()} color={color} />
      {text && (
        <Text style={[styles.text, { color }]}>{text}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  text: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
  },
});