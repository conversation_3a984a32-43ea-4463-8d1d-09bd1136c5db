const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configure resolver for TypeScript support
config.resolver.sourceExts.push('ts', 'tsx');
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Ensure Babel transformer handles TypeScript files
config.transformer.babelTransformerPath = require.resolve('metro-react-native-babel-transformer');

// Configure transform ignore patterns to include expo and react-native packages
config.transformer.transformIgnorePatterns = [
  'node_modules/(?!((react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg))'
];

// Configure asset extensions
config.resolver.assetExts.push('bin');

module.exports = config;