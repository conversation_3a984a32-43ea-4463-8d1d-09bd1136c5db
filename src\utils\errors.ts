import { ERROR_MESSAGES } from './constants';

/**
 * Base application error class
 */
export abstract class AppError extends Error {
  abstract readonly code: string;
  abstract readonly severity: 'low' | 'medium' | 'high' | 'critical';
  
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = this.constructor.name;
    
    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Authentication related errors
 */
export class AuthenticationError extends AppError {
  readonly code = 'AUTH_ERROR';
  readonly severity = 'high' as const;
  
  constructor(message: string = ERROR_MESSAGES.AUTHENTICATION_FAILED, cause?: Error) {
    super(message, cause);
  }
}

export class BiometricError extends AppError {
  readonly code = 'BIOMETRIC_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(
    message: string = ERROR_MESSAGES.BIOMETRIC_FAILED,
    public readonly biometricCode?: string,
    cause?: Error
  ) {
    super(message, cause);
  }
}

export class InvalidPasswordError extends AuthenticationError {
  readonly code = 'INVALID_PASSWORD';
  
  constructor(message: string = ERROR_MESSAGES.INVALID_PASSWORD, cause?: Error) {
    super(message, cause);
  }
}

/**
 * Vault related errors
 */
export class VaultError extends AppError {
  readonly code = 'VAULT_ERROR';
  readonly severity = 'high' as const;
  
  constructor(message: string, cause?: Error) {
    super(message, cause);
  }
}

export class VaultLockedError extends VaultError {
  readonly code = 'VAULT_LOCKED';
  
  constructor(message: string = ERROR_MESSAGES.VAULT_LOCKED, cause?: Error) {
    super(message, cause);
  }
}

export class VaultCorruptedError extends VaultError {
  readonly code = 'VAULT_CORRUPTED';
  readonly severity = 'critical' as const;
  
  constructor(message: string = ERROR_MESSAGES.CORRUPTED_DATA, cause?: Error) {
    super(message, cause);
  }
}

/**
 * Storage related errors
 */
export class StorageError extends AppError {
  readonly code = 'STORAGE_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(message: string, cause?: Error) {
    super(message, cause);
  }
}

export class StorageFullError extends StorageError {
  readonly code = 'STORAGE_FULL';
  readonly severity = 'high' as const;
  
  constructor(message: string = ERROR_MESSAGES.STORAGE_FULL, cause?: Error) {
    super(message, cause);
  }
}

export class PermissionDeniedError extends StorageError {
  readonly code = 'PERMISSION_DENIED';
  
  constructor(message: string = ERROR_MESSAGES.PERMISSION_DENIED, cause?: Error) {
    super(message, cause);
  }
}

/**
 * Network related errors
 */
export class NetworkError extends AppError {
  readonly code = 'NETWORK_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(message: string = ERROR_MESSAGES.NETWORK_ERROR, cause?: Error) {
    super(message, cause);
  }
}

export class SyncError extends AppError {
  readonly code = 'SYNC_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(
    message: string = ERROR_MESSAGES.SYNC_FAILED,
    public readonly retryable: boolean = true,
    cause?: Error
  ) {
    super(message, cause);
  }
}

/**
 * Validation related errors
 */
export class ValidationError extends AppError {
  readonly code = 'VALIDATION_ERROR';
  readonly severity = 'low' as const;
  
  constructor(
    message: string,
    public readonly field?: string,
    public readonly errors?: string[],
    cause?: Error
  ) {
    super(message, cause);
  }
}

export class InvalidFormatError extends ValidationError {
  readonly code = 'INVALID_FORMAT';
  
  constructor(message: string = ERROR_MESSAGES.INVALID_FORMAT, field?: string, cause?: Error) {
    super(message, field, undefined, cause);
  }
}

/**
 * Cryptographic errors
 */
export class CryptographicError extends AppError {
  readonly code = 'CRYPTO_ERROR';
  readonly severity = 'critical' as const;
  
  constructor(message: string, cause?: Error) {
    super(message, cause);
  }
}

export class EncryptionError extends CryptographicError {
  readonly code = 'ENCRYPTION_ERROR';
  
  constructor(message: string = 'Encryption failed', cause?: Error) {
    super(message, cause);
  }
}

export class DecryptionError extends CryptographicError {
  readonly code = 'DECRYPTION_ERROR';
  
  constructor(message: string = 'Decryption failed', cause?: Error) {
    super(message, cause);
  }
}

/**
 * Security related errors
 */
export class SecurityError extends AppError {
  readonly code = 'SECURITY_ERROR';
  readonly severity = 'critical' as const;
  
  constructor(message: string, cause?: Error) {
    super(message, cause);
  }
}

export class DeviceCompromisedError extends SecurityError {
  readonly code = 'DEVICE_COMPROMISED';
  
  constructor(message: string = 'Device security compromised', cause?: Error) {
    super(message, cause);
  }
}

export class RateLimitError extends SecurityError {
  readonly code = 'RATE_LIMITED';
  readonly severity = 'medium' as const;
  
  constructor(
    message: string = 'Too many attempts',
    public readonly retryAfter?: number,
    cause?: Error
  ) {
    super(message, cause);
  }
}

/**
 * Error handling utilities
 */
export class ErrorHandler {
  /**
   * Get user-friendly error message
   */
  static getUserFriendlyMessage(error: Error): string {
    if (error instanceof AppError) {
      return error.message;
    }
    
    // Map common error types to user-friendly messages
    if (error.name === 'NetworkError' || error.message.includes('network')) {
      return ERROR_MESSAGES.NETWORK_ERROR;
    }
    
    if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
      return 'Request timed out. Please try again.';
    }
    
    if (error.message.includes('permission')) {
      return ERROR_MESSAGES.PERMISSION_DENIED;
    }
    
    return ERROR_MESSAGES.UNKNOWN_ERROR;
  }
  
  /**
   * Determine if error is retryable
   */
  static isRetryable(error: Error): boolean {
    if (error instanceof NetworkError) return true;
    if (error instanceof SyncError) return error.retryable;
    if (error instanceof StorageError && !(error instanceof StorageFullError)) return true;
    
    return false;
  }
  
  /**
   * Get error severity
   */
  static getSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    if (error instanceof AppError) {
      return error.severity;
    }
    
    // Default severity for unknown errors
    return 'medium';
  }
  
  /**
   * Log error with appropriate level
   */
  static logError(error: Error, context?: string): void {
    const severity = this.getSeverity(error);
    const message = `${context ? `[${context}] ` : ''}${error.message}`;
    
    switch (severity) {
      case 'critical':
        console.error('CRITICAL:', message, error);
        break;
      case 'high':
        console.error('ERROR:', message, error);
        break;
      case 'medium':
        console.warn('WARNING:', message, error);
        break;
      case 'low':
        console.info('INFO:', message, error);
        break;
    }
  }
  
  /**
   * Create error from unknown value
   */
  static fromUnknown(error: unknown, defaultMessage: string = ERROR_MESSAGES.UNKNOWN_ERROR): Error {
    if (error instanceof Error) {
      return error;
    }
    
    if (typeof error === 'string') {
      return new Error(error);
    }
    
    if (error && typeof error === 'object' && 'message' in error) {
      return new Error(String(error.message));
    }
    
    return new Error(defaultMessage);
  }
}

/**
 * Error boundary helper for React components
 */
export interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

export class ErrorBoundaryError extends AppError {
  readonly code = 'COMPONENT_ERROR';
  readonly severity = 'medium' as const;
  
  constructor(
    message: string,
    public readonly errorInfo: ErrorInfo,
    cause?: Error
  ) {
    super(message, cause);
  }
}

/**
 * Async error wrapper
 */
export const wrapAsync = <T extends any[], R>(
  fn: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<[R | null, Error | null]> => {
    try {
      const result = await fn(...args);
      return [result, null];
    } catch (error) {
      return [null, ErrorHandler.fromUnknown(error)];
    }
  };
};

/**
 * Retry wrapper with exponential backoff
 */
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = ErrorHandler.fromUnknown(error);
      
      if (attempt === maxRetries || !ErrorHandler.isRetryable(lastError)) {
        throw lastError;
      }
      
      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
};