import { EncryptionService } from '../crypto/encryption';
import { KeyDerivationService } from '../crypto/keyDerivation';
import { RandomService } from '../crypto/random';
import { AnyVaultEntry, DecryptedVault, EncryptedVault, NewVaultEntry } from '../../types/vault';

export class VaultManager {
  private static instance: VaultManager;
  private decryptedVault: DecryptedVault | null = null;
  private vaultKey: CryptoKey | null = null;

  private constructor() {}

  static getInstance(): VaultManager {
    if (!VaultManager.instance) {
      VaultManager.instance = new VaultManager();
    }
    return VaultManager.instance;
  }

  /**
   * Create a new vault with master password
   */
  async createVault(masterPassword: string): Promise<void> {
    try {
      // Generate salt for key derivation
      const salt = KeyDerivationService.generateSalt();
      
      // Derive vault key from master password
      const vaultKey = await KeyDerivationService.deriveVaultKey(masterPassword, salt);
      
      // Create empty vault
      const vault: DecryptedVault = {
        id: RandomService.generateId(),
        entries: [],
        settings: {
          lockTimeout: 300, // 5 minutes
          biometricEnabled: false,
          syncEnabled: false,
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      // Store vault key and decrypted vault in memory
      this.vaultKey = vaultKey;
      this.decryptedVault = vault;

      // Encrypt and save vault
      await this.saveVault();
    } catch (error) {
      throw new Error(`Failed to create vault: ${error.message}`);
    }
  }

  /**
   * Unlock vault with master password
   */
  async unlockVault(masterPassword: string): Promise<DecryptedVault> {
    try {
      // Load encrypted vault from storage
      const encryptedVault = await this.loadEncryptedVault();
      if (!encryptedVault) {
        throw new Error('No vault found');
      }

      // Derive key from password and stored salt
      const salt = this.base64ToUint8Array(encryptedVault.salt);
      const vaultKey = await KeyDerivationService.deriveVaultKey(masterPassword, salt);

      // Decrypt vault
      const decryptedData = await EncryptionService.decrypt({
        ciphertext: encryptedVault.ciphertext,
        iv: encryptedVault.iv,
        tag: encryptedVault.tag,
      }, vaultKey);

      const vault: DecryptedVault = JSON.parse(decryptedData);

      // Store in memory
      this.vaultKey = vaultKey;
      this.decryptedVault = vault;

      return vault;
    } catch (error) {
      throw new Error(`Failed to unlock vault: ${error.message}`);
    }
  }

  /**
   * Lock vault (clear from memory)
   */
  lockVault(): void {
    this.vaultKey = null;
    this.decryptedVault = null;
  }

  /**
   * Add new entry to vault
   */
  async addEntry(newEntry: NewVaultEntry): Promise<AnyVaultEntry> {
    if (!this.decryptedVault) {
      throw new Error('Vault is locked');
    }

    const entry: AnyVaultEntry = {
      ...newEntry,
      id: RandomService.generateId(),
      createdAt: Date.now(),
      updatedAt: Date.now(),
    } as AnyVaultEntry;

    this.decryptedVault.entries.push(entry);
    this.decryptedVault.updatedAt = Date.now();

    await this.saveVault();
    return entry;
  }

  /**
   * Update existing entry
   */
  async updateEntry(id: string, updates: Partial<AnyVaultEntry>): Promise<AnyVaultEntry> {
    if (!this.decryptedVault) {
      throw new Error('Vault is locked');
    }

    const entryIndex = this.decryptedVault.entries.findIndex(entry => entry.id === id);
    if (entryIndex === -1) {
      throw new Error('Entry not found');
    }

    const updatedEntry = {
      ...this.decryptedVault.entries[entryIndex],
      ...updates,
      updatedAt: Date.now(),
    };

    this.decryptedVault.entries[entryIndex] = updatedEntry;
    this.decryptedVault.updatedAt = Date.now();

    await this.saveVault();
    return updatedEntry;
  }

  /**
   * Delete entry from vault
   */
  async deleteEntry(id: string): Promise<void> {
    if (!this.decryptedVault) {
      throw new Error('Vault is locked');
    }

    const entryIndex = this.decryptedVault.entries.findIndex(entry => entry.id === id);
    if (entryIndex === -1) {
      throw new Error('Entry not found');
    }

    this.decryptedVault.entries.splice(entryIndex, 1);
    this.decryptedVault.updatedAt = Date.now();

    await this.saveVault();
  }

  /**
   * Get current vault (if unlocked)
   */
  getVault(): DecryptedVault | null {
    return this.decryptedVault;
  }

  /**
   * Search entries
   */
  searchEntries(query: string): AnyVaultEntry[] {
    if (!this.decryptedVault) {
      return [];
    }

    const lowercaseQuery = query.toLowerCase();
    return this.decryptedVault.entries.filter(entry => 
      entry.title.toLowerCase().includes(lowercaseQuery) ||
      ('subtitle' in entry && entry.subtitle?.toLowerCase().includes(lowercaseQuery))
    );
  }

  /**
   * Save vault to storage (encrypted)
   */
  private async saveVault(): Promise<void> {
    if (!this.decryptedVault || !this.vaultKey) {
      throw new Error('No vault or key available');
    }

    try {
      // Encrypt vault data
      const vaultData = JSON.stringify(this.decryptedVault);
      const encrypted = await EncryptionService.encrypt(vaultData, this.vaultKey);

      // Create encrypted vault structure
      const encryptedVault: EncryptedVault = {
        version: 1,
        salt: this.uint8ArrayToBase64(KeyDerivationService.generateSalt()), // This should be stored from creation
        iv: encrypted.iv,
        ciphertext: encrypted.ciphertext,
        tag: encrypted.tag,
        timestamp: Date.now(),
      };

      // Save to platform storage
      await this.saveEncryptedVault(encryptedVault);
    } catch (error) {
      throw new Error(`Failed to save vault: ${error.message}`);
    }
  }

  /**
   * Platform-specific storage methods (to be implemented)
   */
  private async loadEncryptedVault(): Promise<EncryptedVault | null> {
    // This would use platform-specific storage
    // For now, return null (no vault exists)
    return null;
  }

  private async saveEncryptedVault(vault: EncryptedVault): Promise<void> {
    // This would use platform-specific storage
    console.log('Saving encrypted vault:', vault);
  }

  /**
   * Utility methods
   */
  private uint8ArrayToBase64(array: Uint8Array): string {
    return btoa(String.fromCharCode(...array));
  }

  private base64ToUint8Array(base64: string): Uint8Array {
    return new Uint8Array(atob(base64).split('').map(char => char.charCodeAt(0)));
  }
}