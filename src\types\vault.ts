// Vault data structures and types

export type VaultEntryType = 'password' | 'totp' | 'note';

export interface VaultEntry {
  id: string;
  type: VaultEntryType;
  title: string;
  createdAt: number;
  updatedAt: number;
  favorite?: boolean;
  tags?: string[];
}

export interface PasswordEntry extends VaultEntry {
  type: 'password';
  username?: string;
  password: string;
  url?: string;
  notes?: string;
  customFields?: CustomField[];
}

export interface TOTPEntry extends VaultEntry {
  type: 'totp';
  account: string;
  issuer?: string;
  secret: string;
  algorithm?: 'SHA1' | 'SHA256' | 'SHA512';
  digits?: number;
  period?: number;
}

export interface NoteEntry extends VaultEntry {
  type: 'note';
  content: string;
}

export interface CustomField {
  id: string;
  name: string;
  value: string;
  type: 'text' | 'password' | 'email' | 'url';
  hidden?: boolean;
}

export type AnyVaultEntry = PasswordEntry | TOTPEntry | NoteEntry;

export interface DecryptedVault {
  id: string;
  entries: AnyVaultEntry[];
  settings: VaultSettings;
  createdAt: number;
  updatedAt: number;
}

export interface VaultSettings {
  lockTimeout: number; // seconds
  biometricEnabled: boolean;
  syncEnabled: boolean;
  autoFillEnabled?: boolean;
  securityNotifications?: boolean;
}

export interface EncryptedVault {
  version: number;
  salt: string; // Base64 encoded
  iv: string; // Base64 encoded
  ciphertext: string; // Base64 encoded
  tag: string; // Base64 encoded auth tag
  timestamp: number;
  deviceId?: string;
}

// New entry creation types
export type NewVaultEntry = Omit<AnyVaultEntry, 'id' | 'createdAt' | 'updatedAt'>;
export type NewPasswordEntry = Omit<PasswordEntry, 'id' | 'createdAt' | 'updatedAt'>;
export type NewTOTPEntry = Omit<TOTPEntry, 'id' | 'createdAt' | 'updatedAt'>;
export type NewNoteEntry = Omit<NoteEntry, 'id' | 'createdAt' | 'updatedAt'>;

// Search and filtering
export interface VaultSearchOptions {
  query: string;
  types?: VaultEntryType[];
  tags?: string[];
  favorites?: boolean;
}

export interface VaultSearchResult {
  entry: AnyVaultEntry;
  relevanceScore: number;
  matchedFields: string[];
}

// Password generation
export interface PasswordGeneratorOptions {
  length: number;
  includeUppercase: boolean;
  includeLowercase: boolean;
  includeNumbers: boolean;
  includeSymbols: boolean;
  excludeSimilar: boolean;
  excludeAmbiguous: boolean;
  customSymbols?: string;
}

export interface GeneratedPassword {
  password: string;
  strength: PasswordStrength;
  entropy: number;
}

export interface PasswordStrength {
  score: number; // 0-4
  label: 'Very Weak' | 'Weak' | 'Fair' | 'Good' | 'Strong';
  feedback: string[];
}

// TOTP generation
export interface TOTPCode {
  code: string;
  timeRemaining: number;
  validUntil: number;
}

// Vault statistics
export interface VaultStats {
  totalEntries: number;
  passwordEntries: number;
  totpEntries: number;
  noteEntries: number;
  weakPasswords: number;
  duplicatePasswords: number;
  oldPasswords: number;
  lastUpdated: number;
}