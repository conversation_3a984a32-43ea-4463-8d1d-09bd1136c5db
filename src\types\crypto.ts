// Cryptographic types and interfaces

export interface CryptoConfig {
  // Argon2id configuration
  argon2: {
    timeCost: number;
    memoryCost: number; // in KB
    parallelism: number;
    hashLength: number;
  };
  
  // AES-GCM configuration
  aes: {
    keyLength: number;
    ivLength: number;
    tagLength: number;
  };
  
  // Salt configuration
  salt: {
    length: number;
  };
}

export interface EncryptionResult {
  ciphertext: string; // Base64 encoded
  iv: string; // Base64 encoded
  tag: string; // Base64 encoded authentication tag
}

export interface DecryptionInput {
  ciphertext: string; // Base64 encoded
  iv: string; // Base64 encoded
  tag: string; // Base64 encoded authentication tag
}

export interface KeyDerivationParams {
  password: string;
  salt: Uint8Array;
  iterations?: number;
  keyLength?: number;
}

export interface DerivedKey {
  key: CryptoKey;
  salt: Uint8Array;
  iterations: number;
}

export interface CryptoError extends Error {
  code: CryptographicErrorCode;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export type CryptographicErrorCode =
  | 'ENCRYPTION_FAILED'
  | 'DECRYPTION_FAILED'
  | 'KEY_DERIVATION_FAILED'
  | 'INVALID_KEY'
  | 'INVALID_IV'
  | 'INVALID_TAG'
  | 'INSUFFICIENT_ENTROPY'
  | 'UNSUPPORTED_ALGORITHM'
  | 'CRYPTO_API_UNAVAILABLE';

// Export/Import encryption
export interface ExportEncryptionOptions {
  password: string;
  iterations?: number;
  format?: 'json' | 'binary';
}

export interface EncryptedExport {
  version: number;
  format: 'json' | 'binary';
  data: string; // Base64 encoded encrypted data
  salt: string; // Base64 encoded salt
  iv: string; // Base64 encoded IV
  tag: string; // Base64 encoded auth tag
  checksum: string; // SHA-256 checksum
  timestamp: number;
  metadata?: ExportMetadata;
}

export interface ExportMetadata {
  appVersion: string;
  platform: string;
  entryCount: number;
  exportedBy?: string;
}

// Cryptographic service interface
export interface CryptoService {
  // Key operations
  deriveKey(params: KeyDerivationParams): Promise<DerivedKey>;
  generateSalt(): Uint8Array;
  generateIV(): Uint8Array;
  
  // Encryption/Decryption
  encrypt(plaintext: string, key: CryptoKey): Promise<EncryptionResult>;
  decrypt(encrypted: DecryptionInput, key: CryptoKey): Promise<string>;
  
  // Vault operations
  encryptVault(vault: any, key: CryptoKey): Promise<EncryptionResult>;
  decryptVault(encrypted: DecryptionInput, key: CryptoKey): Promise<any>;
  
  // Export/Import
  encryptForExport(data: string, password: string): Promise<EncryptedExport>;
  decryptFromExport(encrypted: EncryptedExport, password: string): Promise<string>;
  
  // Utility functions
  generateSecureRandom(length: number): Uint8Array;
  calculateHash(data: string): Promise<string>;
  constantTimeCompare(a: Uint8Array, b: Uint8Array): boolean;
}

// Memory protection types
export interface SecureMemoryOptions {
  clearOnTimeout?: boolean;
  clearOnBackground?: boolean;
  timeoutMs?: number;
}

export interface SensitiveData {
  data: any;
  options: SecureMemoryOptions;
  createdAt: number;
  lastAccessed: number;
}