console.log("Loaded app/(tabs)/generator.tsx");
import React from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import PasswordGenerator from '../../components/ui/PasswordGenerator';

export default function GeneratorScreen() {
  const handlePasswordGenerated = (password: string) => {
    console.log('Generated password:', password);
    // Handle the generated password (e.g., copy to clipboard, save to vault)
  };

  return (
    <SafeAreaView style={styles.container}>
      <PasswordGenerator 
        onPasswordGenerated={handlePasswordGenerated}
        initialLength={16}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
});