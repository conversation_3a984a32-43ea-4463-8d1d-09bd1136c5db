# Supabase Database Schema

## Overview

TWOFAI uses Supabase as an encrypted blob storage service. The database stores only encrypted vault data and metadata - no plaintext user data ever reaches the server.

## Database Tables

### vaults
Primary table for storing encrypted vault data.

```sql
CREATE TABLE vaults (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  vault_blob TEXT NOT NULL,  -- Base64 encoded encrypted vault
  salt TEXT NOT NULL,        -- Base64 encoded salt for key derivation
  version INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  last_sync_at TIMESTAMPTZ,
  device_name TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  
  -- Ensure one active vault per user
  CONSTRAINT unique_active_vault_per_user 
    EXCLUDE (user_id WITH =) WHERE (is_active = true)
);

-- Indexes for performance
CREATE INDEX idx_vaults_user_id ON vaults(user_id);
CREATE INDEX idx_vaults_device_id ON vaults(device_id);
CREATE INDEX idx_vaults_updated_at ON vaults(updated_at);
```

### vault_sync_log
Track sync operations for conflict resolution and debugging.

```sql
CREATE TABLE vault_sync_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vault_id UUID NOT NULL REFERENCES vaults(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  operation TEXT NOT NULL CHECK (operation IN ('upload', 'download', 'conflict')),
  sync_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  data_hash TEXT, -- SHA-256 of vault_blob for integrity checking
  conflict_resolution TEXT, -- 'local_wins', 'remote_wins', 'manual'
  error_message TEXT,
  metadata JSONB DEFAULT '{}'
);

CREATE INDEX idx_sync_log_vault_id ON vault_sync_log(vault_id);
CREATE INDEX idx_sync_log_timestamp ON vault_sync_log(sync_timestamp);
```

### user_preferences
Store user settings and preferences (non-sensitive).

```sql
CREATE TABLE user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  auto_lock_timeout INTEGER DEFAULT 300, -- seconds
  biometric_enabled BOOLEAN DEFAULT false,
  sync_enabled BOOLEAN DEFAULT true,
  theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
  backup_reminder_days INTEGER DEFAULT 30,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  UNIQUE(user_id)
);
```

## Row Level Security (RLS)

### vaults table policies
```sql
-- Enable RLS
ALTER TABLE vaults ENABLE ROW LEVEL SECURITY;

-- Users can only access their own vaults
CREATE POLICY "Users can view own vaults" 
  ON vaults FOR SELECT 
  TO authenticated 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own vaults" 
  ON vaults FOR INSERT 
  TO authenticated 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own vaults" 
  ON vaults FOR UPDATE 
  TO authenticated 
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own vaults" 
  ON vaults FOR DELETE 
  TO authenticated 
  USING (auth.uid() = user_id);
```

### vault_sync_log table policies
```sql
ALTER TABLE vault_sync_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own sync logs" 
  ON vault_sync_log FOR SELECT 
  TO authenticated 
  USING (
    EXISTS (
      SELECT 1 FROM vaults 
      WHERE vaults.id = vault_sync_log.vault_id 
      AND vaults.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert own sync logs" 
  ON vault_sync_log FOR INSERT 
  TO authenticated 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM vaults 
      WHERE vaults.id = vault_sync_log.vault_id 
      AND vaults.user_id = auth.uid()
    )
  );
```

### user_preferences table policies
```sql
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own preferences" 
  ON user_preferences FOR ALL 
  TO authenticated 
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);
```

## Sync Strategy

### Last-Write-Wins Algorithm
```typescript
interface SyncConflictResolution {
  strategy: 'last_write_wins' | 'manual_merge';
  localTimestamp: number;
  remoteTimestamp: number;
  resolution: 'local' | 'remote' | 'conflict';
}
```

### Sync Operations

#### Upload Flow
1. Check if remote vault exists and get `updated_at`
2. Compare with local `updated_at`
3. If local is newer or equal, upload
4. If remote is newer, trigger conflict resolution
5. Log sync operation

#### Download Flow
1. Fetch remote vault with `updated_at`
2. Compare with local `updated_at`
3. If remote is newer, download and decrypt
4. If local is newer, skip download
5. Log sync operation

#### Conflict Resolution
1. Present both versions to user (if possible)
2. Allow user to choose or merge
3. Update vault with resolution
4. Log conflict and resolution method

## Authentication Integration

### Supabase Auth Setup
```typescript
// Auth configuration
const supabaseConfig = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    flowType: 'pkce'
  }
};
```

### User Registration Flow
1. User creates account with email/password
2. Email verification (optional, configurable)
3. Create initial `user_preferences` record
4. No vault created until first local vault setup

### Session Management
- JWT tokens for API authentication
- Automatic token refresh
- Secure session storage in device keychain
- Session timeout handling

## Data Migration

### Schema Versioning
```sql
-- Migration tracking
CREATE TABLE schema_migrations (
  version INTEGER PRIMARY KEY,
  applied_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  description TEXT NOT NULL
);
```

### Vault Data Versioning
- Each vault blob includes version number
- Backward compatibility for older vault formats
- Migration functions for schema updates
- Rollback capability for failed migrations

## Performance Optimizations

### Database Indexes
- Composite indexes for common query patterns
- Partial indexes for active vaults only
- Regular VACUUM and ANALYZE operations

### Query Optimization
```sql
-- Efficient vault lookup
SELECT vault_blob, salt, updated_at 
FROM vaults 
WHERE user_id = $1 AND is_active = true
LIMIT 1;

-- Recent sync history
SELECT operation, sync_timestamp, conflict_resolution
FROM vault_sync_log
WHERE vault_id = $1
ORDER BY sync_timestamp DESC
LIMIT 10;
```

### Connection Management
- Connection pooling for mobile clients
- Retry logic with exponential backoff
- Offline queue for failed operations
- Batch operations where possible

## Security Considerations

### Data Protection
- All sensitive data encrypted before database storage
- No plaintext passwords, keys, or vault content in database
- Database backups contain only encrypted data
- SSL/TLS for all database connections

### Access Control
- Row-level security enforced at database level
- No admin access to user vault content
- Audit logging for administrative operations
- Regular security assessments

### Compliance
- GDPR compliance through data encryption and user control
- Data retention policies configurable per user
- Right to erasure through secure deletion
- Data portability through encrypted export

## Monitoring and Alerting

### Key Metrics
- Sync success/failure rates
- Authentication failure patterns
- Database performance metrics
- Storage usage trends

### Error Handling
- Graceful degradation for sync failures
- Retry mechanisms with circuit breakers
- User-friendly error messages
- Detailed logging for debugging

### Backup and Recovery
- Automated encrypted database backups
- Point-in-time recovery capability
- Cross-region replication for availability
- Disaster recovery procedures