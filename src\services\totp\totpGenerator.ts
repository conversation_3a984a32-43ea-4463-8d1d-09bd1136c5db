import { TOTPCode } from '../../types/vault';

export class TOTPGenerator {
  /**
   * Generate TOTP code from secret
   */
  static async generateTOTP(
    secret: string,
    period: number = 30,
    digits: number = 6,
    algorithm: 'SHA1' | 'SHA256' | 'SHA512' = 'SHA1'
  ): Promise<TOTPCode> {
    try {
      const now = Math.floor(Date.now() / 1000);
      const counter = Math.floor(now / period);
      
      // Decode base32 secret
      const secretBytes = this.base32Decode(secret);
      
      // Create counter buffer
      const counterBuffer = new ArrayBuffer(8);
      const counterView = new DataView(counterBuffer);
      counterView.setUint32(4, counter, false); // Big-endian
      
      // Import secret as HMAC key
      const key = await crypto.subtle.importKey(
        'raw',
        secretBytes,
        { name: 'HMAC', hash: algorithm },
        false,
        ['sign']
      );
      
      // Generate HMAC
      const hmacBuffer = await crypto.subtle.sign('HMAC', key, counterBuffer);
      const hmac = new Uint8Array(hmacBuffer);
      
      // Dynamic truncation
      const offset = hmac[hmac.length - 1] & 0x0f;
      const code = (
        ((hmac[offset] & 0x7f) << 24) |
        ((hmac[offset + 1] & 0xff) << 16) |
        ((hmac[offset + 2] & 0xff) << 8) |
        (hmac[offset + 3] & 0xff)
      ) % Math.pow(10, digits);
      
      const codeString = code.toString().padStart(digits, '0');
      const timeRemaining = period - (now % period);
      const validUntil = now + timeRemaining;
      
      return {
        code: codeString,
        timeRemaining,
        validUntil,
      };
    } catch (error) {
      throw new Error(`TOTP generation failed: ${error.message}`);
    }
  }

  /**
   * Validate TOTP secret format
   */
  static validateSecret(secret: string): boolean {
    const cleanSecret = secret.replace(/\s/g, '').toUpperCase();
    return /^[A-Z2-7]+=*$/.test(cleanSecret) && cleanSecret.length >= 16;
  }

  /**
   * Base32 decode implementation
   */
  private static base32Decode(encoded: string): Uint8Array {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    const cleanInput = encoded.replace(/\s/g, '').toUpperCase().replace(/=+$/, '');
    
    let bits = '';
    for (const char of cleanInput) {
      const index = alphabet.indexOf(char);
      if (index === -1) {
        throw new Error('Invalid base32 character');
      }
      bits += index.toString(2).padStart(5, '0');
    }
    
    const bytes: number[] = [];
    for (let i = 0; i < bits.length; i += 8) {
      if (i + 8 <= bits.length) {
        bytes.push(parseInt(bits.slice(i, i + 8), 2));
      }
    }
    
    return new Uint8Array(bytes);
  }

  /**
   * Generate mock TOTP for demo purposes
   */
  static generateMockTOTP(period: number = 30, digits: number = 6): TOTPCode {
    const now = Math.floor(Date.now() / 1000);
    const timeRemaining = period - (now % period);
    const validUntil = now + timeRemaining;
    
    // Generate deterministic code based on time window
    const timeWindow = Math.floor(now / period);
    const code = (timeWindow % Math.pow(10, digits)).toString().padStart(digits, '0');
    
    return {
      code,
      timeRemaining,
      validUntil,
    };
  }
}