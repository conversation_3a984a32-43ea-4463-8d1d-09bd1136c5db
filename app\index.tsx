console.log("Loaded app/index.tsx");
import React, { useEffect } from 'react';
import { router } from 'expo-router';

export default function IndexScreen() {
  useEffect(() => {
    // Check if user has set up vault
    const hasVault = false; // This would check actual vault existence
    
    if (hasVault) {
      router.replace('/auth/unlock');
    } else {
      router.replace('/auth/setup');
    }
  }, []);

  return null;
}