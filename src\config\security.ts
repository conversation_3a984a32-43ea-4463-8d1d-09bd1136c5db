import { CryptoConfig, PasswordPolicy, RateLimitConfig } from '../types';

// Cryptographic configuration
export const CRYPTO_CONFIG: CryptoConfig = {
  argon2: {
    timeCost: 3,
    memoryCost: 65536, // 64MB
    parallelism: 1,
    hashLength: 32,
  },
  aes: {
    keyLength: 256,
    ivLength: 12, // 96 bits for GCM
    tagLength: 16, // 128 bits for GCM
  },
  salt: {
    length: 32,
  },
};

// Password policy
export const PASSWORD_POLICY: PasswordPolicy = {
  minLength: 12,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSymbols: true,
  forbiddenPasswords: [
    'password',
    '123456789',
    'qwerty',
    'admin',
    'letmein',
    'welcome',
    'monkey',
    'dragon',
  ],
};

// Rate limiting configuration
export const RATE_LIMIT_CONFIG: RateLimitConfig = {
  maxAttempts: 5,
  windowMs: 15 * 60 * 1000, // 15 minutes
  lockoutDurationMs: 30 * 60 * 1000, // 30 minutes
  exponentialBackoff: true,
};

// Session configuration
export const SESSION_CONFIG = {
  defaultLockTimeout: 5 * 60, // 5 minutes in seconds
  maxLockTimeout: 60 * 60, // 1 hour in seconds
  minLockTimeout: 30, // 30 seconds
  inactivityThreshold: 30, // 30 seconds
  backgroundLockDelay: 1000, // 1 second
};

// Security event thresholds
export const SECURITY_THRESHOLDS = {
  maxFailedAttempts: 3,
  suspiciousActivityThreshold: 10,
  rateLimitThreshold: 5,
  securityEventRetention: 30 * 24 * 60 * 60 * 1000, // 30 days
};

// Biometric configuration
export const BIOMETRIC_CONFIG = {
  fallbackToPassword: true,
  maxRetries: 3,
  promptMessage: 'Authenticate to unlock TWOFAI',
  cancelButtonText: 'Cancel',
  fallbackButtonText: 'Use Password',
  disableDeviceCredential: false,
};

// Vault configuration
export const VAULT_CONFIG = {
  maxEntries: 10000,
  maxFieldLength: 10000,
  maxNoteLength: 50000,
  maxCustomFields: 50,
  defaultBackupReminder: 30, // days
  maxBackupAge: 90, // days
};

// Export configuration
export const EXPORT_CONFIG = {
  defaultIterations: 100000,
  maxFileSize: 50 * 1024 * 1024, // 50MB
  allowedFormats: ['json'] as const,
  compressionEnabled: true,
  checksumAlgorithm: 'SHA-256' as const,
};

// Network security
export const NETWORK_CONFIG = {
  timeout: 30000, // 30 seconds
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  userAgent: 'TWOFAI/1.0',
  allowInsecureConnections: false,
  certificatePinning: true,
};

// Platform-specific security settings
export const PLATFORM_SECURITY = {
  ios: {
    useSecureEnclave: true,
    keychainAccessGroup: undefined,
    backgroundProtection: true,
    screenshotProtection: true,
  },
  android: {
    useHardwareKeystore: true,
    requireScreenLock: true,
    allowDebugBuild: false,
    detectRoot: true,
  },
  web: {
    requireHTTPS: true,
    useWebCrypto: true,
    detectDevTools: true,
    csp: {
      'default-src': "'self'",
      'script-src': "'self' 'unsafe-inline'",
      'style-src': "'self' 'unsafe-inline'",
      'img-src': "'self' data: https:",
      'connect-src': "'self' wss: https:",
      'frame-ancestors': "'none'",
    },
  },
};

// Development vs Production settings
export const getSecurityConfig = (isDevelopment: boolean = __DEV__) => {
  if (isDevelopment) {
    return {
      ...CRYPTO_CONFIG,
      argon2: {
        ...CRYPTO_CONFIG.argon2,
        timeCost: 1, // Faster for development
        memoryCost: 1024, // Less memory for development
      },
      enableLogging: true,
      skipCertificateValidation: false, // Still validate in dev
      allowInsecureConnections: false, // Never allow insecure
    };
  }

  return {
    ...CRYPTO_CONFIG,
    enableLogging: false,
    skipCertificateValidation: false,
    allowInsecureConnections: false,
  };
};

// Security constants
export const SECURITY_CONSTANTS = {
  VAULT_VERSION: 1,
  EXPORT_VERSION: 1,
  MIN_SUPPORTED_VERSION: 1,
  MAX_IV_REUSE_THRESHOLD: 0, // Never allow IV reuse
  MIN_ENTROPY_BITS: 128,
  SECURE_RANDOM_POOL_SIZE: 1024,
} as const;