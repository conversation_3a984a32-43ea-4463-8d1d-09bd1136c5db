// Synchronization types and interfaces

export interface SyncConfiguration {
  enabled: boolean;
  serverUrl: string;
  apiKey: string;
  deviceId: string;
  userId: string;
  conflictResolution: ConflictResolutionStrategy;
  syncInterval: number; // minutes
  maxRetries: number;
  retryBackoffMs: number;
}

export type ConflictResolutionStrategy = 
  | 'last_write_wins'
  | 'manual_merge'
  | 'keep_local'
  | 'keep_remote';

export type SyncState = 'idle' | 'syncing' | 'conflict' | 'error' | 'offline';

export interface SyncStatus {
  state: SyncState;
  lastSyncTime: number;
  nextSyncTime?: number;
  pendingOperations: number;
  hasConflicts: boolean;
  error?: SyncError;
}

export interface SyncOperation {
  id: string;
  type: 'upload' | 'download' | 'delete';
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  vaultData?: EncryptedVault;
  deviceId: string;
}

export interface SyncResult {
  success: boolean;
  hasConflict: boolean;
  conflictResolution?: ConflictResolution;
  syncedAt: number;
  error?: SyncError;
  queued?: boolean;
}

export interface ConflictResolution {
  localVault: EncryptedVault;
  remoteVault: EncryptedVault;
  conflictType: ConflictType;
  resolution: ConflictResolutionStrategy;
  resolvedVault?: EncryptedVault;
  userChoice?: 'local' | 'remote' | 'merged';
}

export type ConflictType = 'timestamp' | 'content' | 'both';

export interface ConflictDetection {
  hasConflict: boolean;
  conflictType: ConflictType;
  localTimestamp: number;
  remoteTimestamp: number;
  localHash: string;
  remoteHash: string;
  conflictDetails?: ConflictDetails[];
}

export interface ConflictDetails {
  field: string;
  localValue: any;
  remoteValue: any;
  action: 'add' | 'modify' | 'delete';
}

export interface SyncError extends Error {
  code: SyncErrorCode;
  retryable: boolean;
  retryAfter?: number;
  details?: any;
}

export type SyncErrorCode =
  | 'NETWORK_ERROR'
  | 'AUTHENTICATION_ERROR'
  | 'SERVER_ERROR'
  | 'CONFLICT_ERROR'
  | 'QUOTA_EXCEEDED'
  | 'RATE_LIMITED'
  | 'VAULT_CORRUPTED'
  | 'VERSION_MISMATCH'
  | 'DEVICE_NOT_AUTHORIZED';

// Sync service interface
export interface SyncService {
  // Sync operations
  syncVault(vault: EncryptedVault): Promise<SyncResult>;
  uploadVault(vault: EncryptedVault): Promise<SyncResult>;
  downloadVault(): Promise<SyncResult>;
  
  // Conflict resolution
  resolveConflict(resolution: ConflictResolution): Promise<SyncResult>;
  detectConflict(local: EncryptedVault, remote: EncryptedVault): ConflictDetection;
  
  // Status and control
  getSyncStatus(): Promise<SyncStatus>;
  pauseSync(): Promise<void>;
  resumeSync(): Promise<void>;
  forcSync(): Promise<SyncResult>;
  
  // Configuration
  updateSyncConfig(config: Partial<SyncConfiguration>): Promise<void>;
  getSyncConfig(): Promise<SyncConfiguration>;
  
  // Offline operations
  queueOperation(operation: SyncOperation): Promise<void>;
  processOfflineQueue(): Promise<SyncResult[]>;
  clearOfflineQueue(): Promise<void>;
}

// Network status
export interface NetworkStatus {
  isConnected: boolean;
  connectionType: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  isMetered: boolean;
  signalStrength?: number;
}

// Sync events
export interface SyncEvent {
  type: SyncEventType;
  timestamp: number;
  data?: any;
}

export type SyncEventType =
  | 'sync_started'
  | 'sync_completed'
  | 'sync_failed'
  | 'conflict_detected'
  | 'conflict_resolved'
  | 'network_changed'
  | 'auth_expired';

// Remote vault data structure
export interface RemoteVaultData {
  id: string;
  user_id: string;
  device_id: string;
  vault_blob: string; // Base64 encoded encrypted vault
  salt: string;
  version: number;
  created_at: string;
  updated_at: string;
  last_sync_at?: string;
  device_name?: string;
  is_active: boolean;
}