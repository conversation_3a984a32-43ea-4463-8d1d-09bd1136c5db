import { Platform } from 'react-native';

/**
 * Platform detection utilities
 */
export const PlatformUtils = {
  /**
   * Check if running on iOS
   */
  isIOS: Platform.OS === 'ios',
  
  /**
   * Check if running on Android
   */
  isAndroid: Platform.OS === 'android',
  
  /**
   * Check if running on Web
   */
  isWeb: Platform.OS === 'web',
  
  /**
   * Check if running on mobile (iOS or Android)
   */
  isMobile: Platform.OS === 'ios' || Platform.OS === 'android',
  
  /**
   * Get platform name
   */
  getPlatform: (): string => Platform.OS,
  
  /**
   * Get platform version
   */
  getPlatformVersion: (): string | number => Platform.Version,
  
  /**
   * Check if platform supports biometrics
   */
  supportsBiometrics: (): boolean => {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if platform supports secure storage
   */
  supportsSecureStorage: (): boolean => {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if platform supports background app refresh
   */
  supportsBackgroundRefresh: (): boolean => {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if platform supports push notifications
   */
  supportsPushNotifications: (): boolean => {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if platform supports file system access
   */
  supportsFileSystem: (): boolean => {
    return Platform.OS === 'ios' || Platform.OS === 'android' || Platform.OS === 'web';
  },
  
  /**
   * Check if platform supports camera
   */
  supportsCamera: (): boolean => {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if platform supports haptic feedback
   */
  supportsHaptics: (): boolean => {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Get platform-specific storage path
   */
  getStoragePath: (): string => {
    switch (Platform.OS) {
      case 'ios':
        return 'Documents/';
      case 'android':
        return 'files/';
      case 'web':
        return 'localStorage/';
      default:
        return 'storage/';
    }
  },
  
  /**
   * Get platform-specific secure storage key prefix
   */
  getSecureStoragePrefix: (): string => {
    switch (Platform.OS) {
      case 'ios':
        return 'twofai.ios.';
      case 'android':
        return 'twofai.android.';
      case 'web':
        return 'twofai.web.';
      default:
        return 'twofai.';
    }
  },
  
  /**
   * Check if running in development mode
   */
  isDevelopment: (): boolean => {
    return __DEV__;
  },
  
  /**
   * Check if running in production mode
   */
  isProduction: (): boolean => {
    return !__DEV__;
  },
  
  /**
   * Get user agent string (web only)
   */
  getUserAgent: (): string => {
    if (Platform.OS === 'web' && typeof navigator !== 'undefined') {
      return navigator.userAgent;
    }
    return `TWOFAI/${Platform.OS}`;
  },
  
  /**
   * Check if device has hardware keyboard
   */
  hasHardwareKeyboard: (): boolean => {
    // This would need platform-specific implementation
    return Platform.OS === 'web';
  },
  
  /**
   * Check if device supports dark mode
   */
  supportsDarkMode: (): boolean => {
    return true; // All platforms support dark mode
  },
  
  /**
   * Get platform-specific font family
   */
  getSystemFont: (): string => {
    switch (Platform.OS) {
      case 'ios':
        return 'System';
      case 'android':
        return 'Roboto';
      case 'web':
        return '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      default:
        return 'System';
    }
  },
  
  /**
   * Get platform-specific monospace font
   */
  getMonospaceFont: (): string => {
    switch (Platform.OS) {
      case 'ios':
        return 'Menlo';
      case 'android':
        return 'monospace';
      case 'web':
        return 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace';
      default:
        return 'monospace';
    }
  },
  
  /**
   * Check if platform supports clipboard
   */
  supportsClipboard: (): boolean => {
    return true; // All platforms support clipboard
  },
  
  /**
   * Check if platform supports sharing
   */
  supportsSharing: (): boolean => {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if platform supports deep linking
   */
  supportsDeepLinking: (): boolean => {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Get platform-specific safe area insets
   */
  getSafeAreaInsets: () => {
    // This would typically come from react-native-safe-area-context
    // Returning default values for now
    return {
      top: Platform.OS === 'ios' ? 44 : 24,
      bottom: Platform.OS === 'ios' ? 34 : 0,
      left: 0,
      right: 0,
    };
  },
  
  /**
   * Check if platform supports multiple windows
   */
  supportsMultipleWindows: (): boolean => {
    return Platform.OS === 'web';
  },
  
  /**
   * Check if platform supports offline storage
   */
  supportsOfflineStorage: (): boolean => {
    return true; // All platforms support some form of offline storage
  },
  
  /**
   * Get platform-specific maximum file size
   */
  getMaxFileSize: (): number => {
    switch (Platform.OS) {
      case 'ios':
      case 'android':
        return 50 * 1024 * 1024; // 50MB
      case 'web':
        return 10 * 1024 * 1024; // 10MB (browser limitations)
      default:
        return 50 * 1024 * 1024;
    }
  },
};

/**
 * Platform-specific feature detection
 */
export const FeatureDetection = {
  /**
   * Check if Web Crypto API is available
   */
  hasWebCrypto: (): boolean => {
    return typeof crypto !== 'undefined' && 
           typeof crypto.subtle !== 'undefined';
  },
  
  /**
   * Check if IndexedDB is available
   */
  hasIndexedDB: (): boolean => {
    return typeof indexedDB !== 'undefined';
  },
  
  /**
   * Check if Service Workers are available
   */
  hasServiceWorkers: (): boolean => {
    return typeof navigator !== 'undefined' && 
           'serviceWorker' in navigator;
  },
  
  /**
   * Check if Web Workers are available
   */
  hasWebWorkers: (): boolean => {
    return typeof Worker !== 'undefined';
  },
  
  /**
   * Check if device has touch support
   */
  hasTouch: (): boolean => {
    if (Platform.OS === 'web') {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if device supports vibration
   */
  hasVibration: (): boolean => {
    if (Platform.OS === 'web') {
      return 'vibrate' in navigator;
    }
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if device supports geolocation
   */
  hasGeolocation: (): boolean => {
    if (Platform.OS === 'web') {
      return 'geolocation' in navigator;
    }
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if device supports device motion
   */
  hasDeviceMotion: (): boolean => {
    if (Platform.OS === 'web') {
      return 'DeviceMotionEvent' in window;
    }
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
  
  /**
   * Check if device supports full screen
   */
  hasFullScreen: (): boolean => {
    if (Platform.OS === 'web') {
      return document.fullscreenEnabled || 
             (document as any).webkitFullscreenEnabled ||
             (document as any).mozFullScreenEnabled ||
             (document as any).msFullscreenEnabled;
    }
    return false;
  },
};

/**
 * Device information utilities
 */
export const DeviceInfo = {
  /**
   * Get device type
   */
  getDeviceType: (): 'phone' | 'tablet' | 'desktop' | 'unknown' => {
    if (Platform.OS === 'web') {
      const userAgent = navigator.userAgent.toLowerCase();
      if (/tablet|ipad/.test(userAgent)) return 'tablet';
      if (/mobile|android|iphone/.test(userAgent)) return 'phone';
      return 'desktop';
    }
    
    // For React Native, we'd need additional libraries to detect tablet vs phone
    return Platform.OS === 'ios' || Platform.OS === 'android' ? 'phone' : 'unknown';
  },
  
  /**
   * Get screen dimensions
   */
  getScreenDimensions: () => {
    if (Platform.OS === 'web') {
      return {
        width: window.screen.width,
        height: window.screen.height,
        availableWidth: window.screen.availWidth,
        availableHeight: window.screen.availHeight,
      };
    }
    
    // For React Native, this would come from Dimensions API
    return {
      width: 0,
      height: 0,
      availableWidth: 0,
      availableHeight: 0,
    };
  },
  
  /**
   * Check if device is in landscape mode
   */
  isLandscape: (): boolean => {
    if (Platform.OS === 'web') {
      return window.innerWidth > window.innerHeight;
    }
    
    // For React Native, this would come from Dimensions API
    return false;
  },
  
  /**
   * Get device pixel ratio
   */
  getPixelRatio: (): number => {
    if (Platform.OS === 'web') {
      return window.devicePixelRatio || 1;
    }
    
    // For React Native, this would come from PixelRatio API
    return 1;
  },
};

// Export platform constants
export const PLATFORM_CONSTANTS = {
  IOS: 'ios',
  ANDROID: 'android',
  WEB: 'web',
  CURRENT: Platform.OS,
} as const;