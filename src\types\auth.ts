// Authentication types and interfaces

export interface AuthenticationResult {
  success: boolean;
  vaultKey?: CryptoKey;
  error?: string;
  fallbackToPassword?: boolean;
}

export interface BiometricAuthResult {
  success: boolean;
  biometricKey?: string;
  error?: string;
  errorCode?: BiometricErrorCode;
}

export type BiometricErrorCode = 
  | 'BiometryNotAvailable'
  | 'BiometryNotEnrolled' 
  | 'BiometryLockout'
  | 'BiometryTemporaryLockout'
  | 'UserCancel'
  | 'UserFallback'
  | 'SystemCancel'
  | 'PasscodeNotSet'
  | 'TouchIDNotAvailable'
  | 'TouchIDNotEnrolled'
  | 'TouchIDLockout'
  | 'FaceIDNotAvailable'
  | 'FaceIDNotEnrolled'
  | 'FaceIDLockout';

export interface BiometricCapabilities {
  isAvailable: boolean;
  biometricType: BiometricType | null;
  hasHardwareSupport: boolean;
  isEnrolled: boolean;
}

export type BiometricType = 'fingerprint' | 'face' | 'iris' | 'voice';

export interface SessionContext {
  isLocked: boolean;
  vaultKey: CryptoKey | null;
  lastActivity: number;
  lockTimeout: number;
  biometricEnabled: boolean;
  deviceId: string;
}

export type SessionState = 'locked' | 'unlocking' | 'unlocked' | 'locking' | 'expired';

export interface AuthenticationAttempt {
  timestamp: number;
  method: 'password' | 'biometric';
  success: boolean;
  errorCode?: string;
  ipAddress?: string;
  deviceInfo?: string;
}

export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  metadata?: Record<string, any>;
}

export type SecurityEventType = 
  | 'authentication_failure'
  | 'biometric_change'
  | 'device_compromise'
  | 'jailbreak_detected'
  | 'debugger_detected'
  | 'sync_failure'
  | 'vault_corruption'
  | 'unauthorized_access';

export interface RateLimitConfig {
  maxAttempts: number;
  windowMs: number;
  lockoutDurationMs: number;
  exponentialBackoff: boolean;
}

export interface RateLimitState {
  attempts: number;
  lastAttempt: number;
  isLocked: boolean;
  lockoutUntil?: number;
}

// Master password requirements
export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSymbols: boolean;
  maxLength?: number;
  forbiddenPasswords?: string[];
}

export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  strength: number;
}