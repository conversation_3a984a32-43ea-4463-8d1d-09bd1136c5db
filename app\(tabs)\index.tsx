console.log("Loaded app/(tabs)/index.tsx");
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Plus, Search, Shield, Lock, Key, FileText, Star } from 'lucide-react-native';
import VaultEntry from '../../components/ui/VaultEntry';
import TOTPDisplay from '../../components/ui/TOTPDisplay';

// Mock data for demonstration
const mockEntries = [
  {
    id: '1',
    type: 'password' as const,
    title: 'Gmail Account',
    subtitle: '<EMAIL>',
    lastUsed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    isFavorite: true,
  },
  {
    id: '2',
    type: 'totp' as const,
    title: 'GitHub',
    subtitle: 'github.com',
    lastUsed: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    isFavorite: false,
  },
  {
    id: '3',
    type: 'note' as const,
    title: 'WiFi Password',
    subtitle: 'Home network credentials',
    lastUsed: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    isFavorite: false,
  },
  {
    id: '4',
    type: 'password' as const,
    title: 'Banking App',
    subtitle: 'mybank.com',
    lastUsed: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    isFavorite: true,
  },
];

const mockTOTPEntries = [
  {
    secret: 'JBSWY3DPEHPK3PXP',
    issuer: 'Google',
    account: '<EMAIL>',
  },
  {
    secret: 'HXDMVJECJJWSRB3HWIZR4IFUGFTMXBOZ',
    issuer: 'GitHub',
    account: 'username',
  },
];

export default function VaultScreen() {
  const [showSensitive, setShowSensitive] = useState<Record<string, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('');

  const toggleSensitive = (id: string) => {
    setShowSensitive(prev => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const handleEntryPress = (id: string) => {
    console.log('Entry pressed:', id);
    // Navigate to entry details
  };

  const handleCopy = (id: string) => {
    console.log('Copy pressed for:', id);
    // Implement copy functionality
  };

  const handleTOTPCopy = (code: string) => {
    console.log('TOTP code copied:', code);
    // Implement clipboard copy
  };

  const favoriteEntries = mockEntries.filter(entry => entry.isFavorite);
  const recentEntries = mockEntries.filter(entry => !entry.isFavorite);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>TWOFAI</Text>
          <View style={styles.statusIndicator}>
            <Shield size={16} color="#10b981" />
            <Text style={styles.statusText}>Vault Secured</Text>
          </View>
        </View>
        <TouchableOpacity style={styles.iconButton}>
          <Search size={22} color="#6b7280" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Search size={18} color="#9ca3af" />
          <Text style={styles.searchPlaceholder}>Search your vault...</Text>
        </View>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* TOTP Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>2FA Codes</Text>
            <Text style={styles.sectionCount}>{mockTOTPEntries.length}</Text>
          </View>
          {mockTOTPEntries.map((entry, index) => (
            <TOTPDisplay
              key={index}
              secret={entry.secret}
              issuer={entry.issuer}
              account={entry.account}
              onCopy={handleTOTPCopy}
            />
          ))}
        </View>

        {/* Favorites Section */}
        {favoriteEntries.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleRow}>
                <Star size={18} color="#f59e0b" fill="#f59e0b" />
                <Text style={styles.sectionTitle}>Favorites</Text>
              </View>
              <Text style={styles.sectionCount}>{favoriteEntries.length}</Text>
            </View>
            {favoriteEntries.map((entry) => (
              <VaultEntry
                key={entry.id}
                id={entry.id}
                type={entry.type}
                title={entry.title}
                subtitle={entry.subtitle}
                lastUsed={entry.lastUsed}
                isFavorite={entry.isFavorite}
                onPress={() => handleEntryPress(entry.id)}
                onCopy={() => handleCopy(entry.id)}
                showSensitive={showSensitive[entry.id]}
                onToggleSensitive={() => toggleSensitive(entry.id)}
              />
            ))}
          </View>
        )}

        {/* Recent Items Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Items</Text>
            <Text style={styles.sectionCount}>{recentEntries.length}</Text>
          </View>
          {recentEntries.map((entry) => (
            <VaultEntry
              key={entry.id}
              id={entry.id}
              type={entry.type}
              title={entry.title}
              subtitle={entry.subtitle}
              lastUsed={entry.lastUsed}
              isFavorite={entry.isFavorite}
              onPress={() => handleEntryPress(entry.id)}
              onCopy={() => handleCopy(entry.id)}
              showSensitive={showSensitive[entry.id]}
              onToggleSensitive={() => toggleSensitive(entry.id)}
            />
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.quickAction}>
              <View style={styles.quickActionIcon}>
                <Key size={22} color="#3b82f6" />
              </View>
              <Text style={styles.quickActionText}>Add Password</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickAction}>
              <View style={styles.quickActionIcon}>
                <Shield size={22} color="#10b981" />
              </View>
              <Text style={styles.quickActionText}>Add 2FA</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickAction}>
              <View style={styles.quickActionIcon}>
                <FileText size={22} color="#f59e0b" />
              </View>
              <Text style={styles.quickActionText}>Add Note</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom spacing for FAB */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity style={styles.fab}>
        <Plus size={26} color="#ffffff" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 4,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusText: {
    fontSize: 13,
    color: '#10b981',
    fontWeight: '600',
  },
  iconButton: {
    padding: 10,
    borderRadius: 12,
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  searchPlaceholder: {
    fontSize: 16,
    color: '#9ca3af',
  },
  content: {
    flex: 1,
  },
  section: {
    marginTop: 24,
    marginBottom: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    marginHorizontal: 20,
  },
  sectionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
  },
  sectionCount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748b',
    backgroundColor: '#f1f5f9',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  quickActions: {
    paddingHorizontal: 20,
    gap: 12,
  },
  quickAction: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    paddingVertical: 18,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#f1f5f9',
  },
  quickActionIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#f8fafc',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  quickActionText: {
    fontSize: 17,
    fontWeight: '600',
    color: '#1e293b',
  },
  bottomSpacing: {
    height: 100, // Space for FAB
  },
  fab: {
    position: 'absolute',
    bottom: 32,
    right: 24,
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#3b82f6',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#3b82f6',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
});