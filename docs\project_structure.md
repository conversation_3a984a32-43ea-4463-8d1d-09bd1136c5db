# Project Structure and Architecture

## Overview

TWOFAI follows a modular, scalable architecture designed for cross-platform development. The structure emphasizes separation of concerns, reusability, and maintainability while supporting future expansion to browser extensions, desktop applications, and web dashboards.

## Directory Structure

```
twofai/
├── app/                          # Expo Router screens
│   ├── (tabs)/                   # Tab-based navigation
│   │   ├── _layout.tsx          # Tab navigator configuration
│   │   ├── index.tsx            # Vault screen (home)
│   │   ├── generator.tsx        # Password generator
│   │   └── settings.tsx         # Settings screen
│   ├── auth/                    # Authentication flows
│   │   ├── setup.tsx           # Initial setup
│   │   ├── unlock.tsx          # Vault unlock
│   │   └── biometric-setup.tsx # Biometric configuration
│   ├── vault/                  # Vault management screens
│   │   ├── entry/
│   │   │   └── [id].tsx       # Entry details
│   │   ├── edit/
│   │   │   └── [id].tsx       # Edit entry
│   │   ├── add.tsx            # Add new entry
│   │   └── search.tsx         # Search interface
│   ├── modals/                 # Modal screens
│   │   ├── sync-conflict.tsx  # Conflict resolution
│   │   ├── export-vault.tsx   # Export options
│   │   └── import-vault.tsx   # Import options
│   ├── _layout.tsx            # Root layout
│   └── +not-found.tsx         # 404 screen
├── src/                        # Core application logic
│   ├── components/             # Reusable UI components
│   │   ├── ui/                # Base UI components
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   ├── Card.tsx
│   │   │   ├── Modal.tsx
│   │   │   └── LoadingSpinner.tsx
│   │   ├── security/          # Security-specific components
│   │   │   ├── BiometricPrompt.tsx
│   │   │   ├── SecurityIndicator.tsx
│   │   │   ├── UnlockForm.tsx
│   │   │   └── PasswordStrength.tsx
│   │   ├── vault/             # Vault-related components
│   │   │   ├── VaultEntry.tsx
│   │   │   ├── VaultList.tsx
│   │   │   ├── EntryForm.tsx
│   │   │   ├── TOTPDisplay.tsx
│   │   │   └── SearchBar.tsx
│   │   └── generators/        # Password generation components
│   │       ├── PasswordGenerator.tsx
│   │       ├── GeneratorOptions.tsx
│   │       └── PasswordOutput.tsx
│   ├── hooks/                 # Custom React hooks
│   │   ├── useVault.ts       # Vault state management
│   │   ├── useAuth.ts        # Authentication state
│   │   ├── useBiometric.ts   # Biometric authentication
│   │   ├── useSync.ts        # Sync operations
│   │   ├── useTheme.ts       # Theme management
│   │   └── useFrameworkReady.ts # Framework initialization
│   ├── services/              # Business logic services
│   │   ├── crypto/           # Cryptographic operations
│   │   │   ├── encryption.ts # AES-GCM encryption
│   │   │   ├── keyDerivation.ts # Argon2id implementation
│   │   │   ├── random.ts     # Secure random generation
│   │   │   └── index.ts      # Crypto service exports
│   │   ├── vault/            # Vault management
│   │   │   ├── vaultManager.ts # Main vault operations
│   │   │   ├── entryManager.ts # Entry CRUD operations
│   │   │   ├── searchService.ts # Search functionality
│   │   │   └── index.ts      # Vault service exports
│   │   ├── storage/          # Platform storage
│   │   │   ├── platformStorage.ts # Storage interface
│   │   │   ├── iosStorage.ts     # iOS implementation
│   │   │   ├── androidStorage.ts # Android implementation
│   │   │   ├── webStorage.ts     # Web implementation
│   │   │   └── index.ts          # Storage exports
│   │   ├── sync/             # Synchronization
│   │   │   ├── syncManager.ts    # Sync orchestration
│   │   │   ├── conflictResolver.ts # Conflict resolution
│   │   │   ├── offlineQueue.ts   # Offline operations
│   │   │   └── index.ts          # Sync exports
│   │   ├── auth/             # Authentication services
│   │   │   ├── authManager.ts    # Authentication logic
│   │   │   ├── biometricAuth.ts  # Biometric handling
│   │   │   ├── sessionManager.ts # Session management
│   │   │   └── index.ts          # Auth exports
│   │   └── generators/       # Password generation
│   │       ├── passwordGenerator.ts # Password algorithms
│   │       ├── totpGenerator.ts    # TOTP implementation
│   │       └── index.ts            # Generator exports
│   ├── types/                # TypeScript type definitions
│   │   ├── vault.ts          # Vault data structures
│   │   ├── auth.ts          # Authentication types
│   │   ├── crypto.ts        # Cryptographic types
│   │   ├── storage.ts       # Storage interfaces
│   │   ├── sync.ts          # Sync operation types
│   │   ├── ui.ts            # UI component types
│   │   └── index.ts         # Type exports
│   ├── utils/               # Utility functions
│   │   ├── validation.ts    # Input validation
│   │   ├── formatting.ts    # Data formatting
│   │   ├── constants.ts     # Application constants
│   │   ├── errors.ts        # Error handling utilities
│   │   ├── platform.ts      # Platform detection
│   │   └── index.ts         # Utility exports
│   ├── config/              # Configuration files
│   │   ├── theme.ts         # Theme configuration
│   │   ├── security.ts      # Security settings
│   │   ├── api.ts           # API configuration
│   │   └── index.ts         # Config exports
│   └── contexts/            # React contexts
│       ├── VaultContext.tsx # Vault state context
│       ├── AuthContext.tsx  # Authentication context
│       ├── ThemeContext.tsx # Theme context
│       └── index.ts         # Context exports
├── docs/                    # Technical documentation
│   ├── vault_encryption.md
│   ├── supabase_schema.md
│   ├── platform_storage.md
│   ├── unlock_and_sync.md
│   ├── design_guidelines.md
│   ├── ui_components_and_flows.md
│   ├── project_structure.md
│   ├── security_audit.md
│   ├── test_coverage.md
│   └── architecture_diagrams.md
├── tests/                   # Test files
│   ├── __mocks__/          # Mock implementations
│   ├── components/         # Component tests
│   ├── hooks/              # Hook tests
│   ├── services/           # Service tests
│   ├── utils/              # Utility tests
│   └── integration/        # Integration tests
├── assets/                 # Static assets
│   ├── images/            # Image assets
│   ├── icons/             # Icon files
│   └── fonts/             # Font files (if any)
├── supabase/              # Supabase configuration
│   ├── migrations/        # Database migrations
│   └── config.toml        # Supabase config
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
├── app.json              # Expo configuration
├── babel.config.js       # Babel configuration
└── README.md            # Project documentation
```

## Naming Conventions

### Files and Directories
- **PascalCase**: React components (`VaultEntry.tsx`, `BiometricPrompt.tsx`)
- **camelCase**: Services, utilities, hooks (`vaultManager.ts`, `useAuth.ts`)
- **kebab-case**: Screen routes (`sync-conflict.tsx`, `biometric-setup.tsx`)
- **lowercase**: Configuration files (`tsconfig.json`, `package.json`)

### Code Conventions
- **Interfaces**: PascalCase with descriptive names (`VaultEntry`, `AuthenticationResult`)
- **Types**: PascalCase with `Type` suffix for unions (`VaultEntryType`, `SyncStatusType`)
- **Enums**: PascalCase with descriptive names (`SecurityLevel`, `BiometricType`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_RETRY_ATTEMPTS`, `DEFAULT_LOCK_TIMEOUT`)
- **Functions**: camelCase with verb-noun pattern (`encryptVault`, `validatePassword`)

## Architecture Patterns

### 1. Service Layer Architecture
```typescript
// Service interfaces define contracts
interface VaultService {
  createEntry(entry: NewVaultEntry): Promise<VaultEntry>;
  updateEntry(id: string, updates: Partial<VaultEntry>): Promise<VaultEntry>;
  deleteEntry(id: string): Promise<void>;
  getEntry(id: string): Promise<VaultEntry | null>;
  searchEntries(query: string): Promise<VaultEntry[]>;
}

// Concrete implementations
class DefaultVaultService implements VaultService {
  constructor(
    private cryptoService: CryptoService,
    private storageService: StorageService
  ) {}
  
  async createEntry(entry: NewVaultEntry): Promise<VaultEntry> {
    // Implementation
  }
}
```

### 2. Hook-Based State Management
```typescript
// Custom hooks encapsulate state logic
export const useVault = () => {
  const [vault, setVault] = useState<DecryptedVault | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const vaultService = useVaultService();
  
  const loadVault = useCallback(async () => {
    setIsLoading(true);
    try {
      const loadedVault = await vaultService.loadVault();
      setVault(loadedVault);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [vaultService]);

  return { vault, isLoading, error, loadVault };
};
```

### 3. Context for Global State
```typescript
// Contexts provide global state access
export const VaultProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const vaultState = useVault();
  const syncState = useSync();
  const authState = useAuth();

  const value = useMemo(() => ({
    ...vaultState,
    ...syncState,
    ...authState,
  }), [vaultState, syncState, authState]);

  return (
    <VaultContext.Provider value={value}>
      {children}
    </VaultContext.Provider>
  );
};
```

## Dependency Management

### Dependency Injection Pattern
```typescript
// Service dependencies are injected
class VaultManager {
  constructor(
    private cryptoService: CryptoService,
    private storageService: StorageService,
    private syncService: SyncService
  ) {}
}

// Service factory for dependency resolution
export const createVaultManager = (): VaultManager => {
  const cryptoService = createCryptoService();
  const storageService = createStorageService();
  const syncService = createSyncService();
  
  return new VaultManager(cryptoService, storageService, syncService);
};
```

### Service Provider Pattern
```typescript
// Service context provides dependencies
const ServiceContext = createContext<Services | null>(null);

export const ServiceProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const services = useMemo(() => ({
    vaultService: createVaultService(),
    cryptoService: createCryptoService(),
    storageService: createStorageService(),
    syncService: createSyncService(),
  }), []);

  return (
    <ServiceContext.Provider value={services}>
      {children}
    </ServiceContext.Provider>
  );
};
```

## Module Boundaries

### Core Modules
1. **Authentication** (`src/services/auth/`)
   - User authentication
   - Biometric integration
   - Session management

2. **Cryptography** (`src/services/crypto/`)
   - Encryption/decryption
   - Key derivation
   - Random number generation

3. **Vault Management** (`src/services/vault/`)
   - Vault operations
   - Entry management
   - Search functionality

4. **Storage** (`src/services/storage/`)
   - Platform-specific storage
   - Data persistence
   - Backup/restore

5. **Synchronization** (`src/services/sync/`)
   - Cloud sync
   - Conflict resolution
   - Offline operations

### UI Modules
1. **Base Components** (`src/components/ui/`)
   - Reusable UI elements
   - Design system components
   - Accessibility features

2. **Feature Components** (`src/components/*/`)
   - Domain-specific components
   - Business logic integration
   - User interaction handling

3. **Screens** (`app/`)
   - Screen-level components
   - Navigation setup
   - Route definitions

## Inter-Module Communication

### Event-Driven Architecture
```typescript
// Event emitter for cross-module communication
class EventBus {
  private listeners: Map<string, Function[]> = new Map();

  emit(event: string, data?: any): void {
    const eventListeners = this.listeners.get(event) || [];
    eventListeners.forEach(listener => listener(data));
  }

  on(event: string, listener: Function): void {
    const listeners = this.listeners.get(event) || [];
    listeners.push(listener);
    this.listeners.set(event, listeners);
  }

  off(event: string, listener: Function): void {
    const listeners = this.listeners.get(event) || [];
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }
}

// Usage across modules
export const eventBus = new EventBus();

// In vault service
vaultService.on('vault:unlocked', () => {
  syncService.resumeSync();
});

// In auth service  
authService.emit('vault:unlocked');
```

### Service Communication
```typescript
// Services communicate through well-defined interfaces
interface SyncNotificationService {
  onVaultChanged(vaultId: string): void;
  onConflictDetected(conflict: SyncConflict): void;
}

class VaultService {
  constructor(private syncNotification: SyncNotificationService) {}
  
  async updateEntry(id: string, updates: Partial<VaultEntry>): Promise<VaultEntry> {
    const updatedEntry = await this.performUpdate(id, updates);
    
    // Notify sync service of changes
    this.syncNotification.onVaultChanged(updatedEntry.vaultId);
    
    return updatedEntry;
  }
}
```

## Error Handling Strategy

### Error Types Hierarchy
```typescript
// Base error class
abstract class TWOFAIError extends Error {
  abstract readonly code: string;
  abstract readonly severity: 'low' | 'medium' | 'high' | 'critical';
  
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = this.constructor.name;
  }
}

// Domain-specific errors
class VaultError extends TWOFAIError {
  readonly code = 'VAULT_ERROR';
  readonly severity = 'high' as const;
}

class CryptoError extends TWOFAIError {
  readonly code = 'CRYPTO_ERROR';
  readonly severity = 'critical' as const;
}

class StorageError extends TWOFAIError {
  readonly code = 'STORAGE_ERROR';
  readonly severity = 'medium' as const;
}
```

### Error Propagation
```typescript
// Services handle and transform errors
class VaultService {
  async loadVault(): Promise<DecryptedVault> {
    try {
      const encryptedVault = await this.storageService.loadVault();
      return await this.cryptoService.decryptVault(encryptedVault);
    } catch (error) {
      if (error instanceof StorageError) {
        throw new VaultError('Failed to load vault from storage', error);
      }
      if (error instanceof CryptoError) {
        throw new VaultError('Failed to decrypt vault', error);
      }
      throw error;
    }
  }
}

// Components handle user-facing errors
const VaultScreen: React.FC = () => {
  const { vault, error, loadVault } = useVault();
  
  const handleError = (error: TWOFAIError) => {
    switch (error.severity) {
      case 'critical':
        // Show critical error modal, possibly lock app
        break;
      case 'high':
        // Show error banner with retry option
        break;
      case 'medium':
        // Show toast notification
        break;
      case 'low':
        // Log only, minimal user notification
        break;
    }
  };
  
  useEffect(() => {
    if (error) {
      handleError(error);
    }
  }, [error]);
};
```

## Testing Strategy

### Test Organization
```typescript
// Test structure mirrors source structure
tests/
├── components/
│   ├── ui/
│   │   ├── Button.test.tsx
│   │   └── Input.test.tsx
│   └── vault/
│       ├── VaultEntry.test.tsx
│       └── VaultList.test.tsx
├── services/
│   ├── crypto/
│   │   ├── encryption.test.ts
│   │   └── keyDerivation.test.ts
│   └── vault/
│       └── vaultManager.test.ts
├── hooks/
│   ├── useVault.test.ts
│   └── useAuth.test.ts
└── integration/
    ├── vault-operations.test.ts
    └── sync-flow.test.ts
```

### Mock Strategy
```typescript
// Service mocks for testing
export const mockVaultService: jest.Mocked<VaultService> = {
  createEntry: jest.fn(),
  updateEntry: jest.fn(),
  deleteEntry: jest.fn(),
  getEntry: jest.fn(),
  searchEntries: jest.fn(),
};

// Test utilities
export const createTestVaultEntry = (overrides?: Partial<VaultEntry>): VaultEntry => ({
  id: 'test-id',
  type: 'password',
  title: 'Test Entry',
  username: 'testuser',
  password: 'testpassword',
  createdAt: Date.now(),
  updatedAt: Date.now(),
  ...overrides,
});
```

## Build and Deployment

### Build Configuration
```typescript
// Environment-specific configurations
const config = {
  development: {
    apiUrl: 'http://localhost:3000',
    enableDebugLogs: true,
    cryptoIterations: 1000, // Faster for development
  },
  production: {
    apiUrl: 'https://api.twofai.com',
    enableDebugLogs: false,
    cryptoIterations: 100000, // Secure for production
  },
};

export const getConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  return config[env as keyof typeof config];
};
```

### Platform-Specific Builds
```json
{
  "scripts": {
    "dev": "expo start",
    "build:ios": "expo build:ios",
    "build:android": "expo build:android",
    "build:web": "expo build:web",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/ app/ --ext .ts,.tsx",
    "type-check": "tsc --noEmit"
  }
}
```

## Future Scalability

### Extension Points
- **Plugin Architecture**: Support for third-party integrations
- **Theme System**: Customizable themes and branding
- **Localization**: Multi-language support infrastructure
- **API Versioning**: Backward-compatible API evolution

### Shared Code Strategy
```typescript
// Shared core library for cross-platform use
@twofai/core/
├── src/
│   ├── crypto/      # Crypto operations
│   ├── vault/       # Vault management
│   ├── types/       # Shared types
│   └── utils/       # Utility functions
└── package.json

// Platform-specific packages
@twofai/mobile/      # React Native app
@twofai/extension/   # Browser extension
@twofai/desktop/     # Electron app
@twofai/web/         # Web dashboard
```

This structure provides a solid foundation for building TWOFAI as a secure, maintainable, and scalable password manager while supporting future platform expansion and feature development.