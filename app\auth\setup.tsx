import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Shield, Check, Eye, EyeOff } from 'lucide-react-native';
import { router } from 'expo-router';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';

export default function SetupScreen() {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const getPasswordStrength = (pwd: string) => {
    let score = 0;
    if (pwd.length >= 8) score++;
    if (pwd.length >= 12) score++;
    if (/[A-Z]/.test(pwd)) score++;
    if (/[a-z]/.test(pwd)) score++;
    if (/\d/.test(pwd)) score++;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd)) score++;

    if (score <= 2) return { label: 'Weak', color: '#ef4444', progress: 33 };
    if (score <= 4) return { label: 'Good', color: '#f59e0b', progress: 66 };
    return { label: 'Strong', color: '#10b981', progress: 100 };
  };

  const validatePasswords = () => {
    if (password.length < 8) {
      Alert.alert('Error', 'Password must be at least 8 characters long');
      return false;
    }
    
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }
    
    return true;
  };

  const handleSetup = async () => {
    if (!validatePasswords()) return;

    setIsLoading(true);

    // Simulate vault creation
    setTimeout(() => {
      setIsLoading(false);
      Alert.alert(
        'Vault Created',
        'Your secure vault has been created successfully!',
        [{ text: 'Continue', onPress: () => router.replace('/auth/unlock') }]
      );
    }, 2000);
  };

  const strength = getPasswordStrength(password);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Shield size={48} color="#3b82f6" />
          </View>
          <Text style={styles.title}>Create Your Vault</Text>
          <Text style={styles.subtitle}>
            Set up a master password to secure your passwords and 2FA codes
          </Text>
        </View>

        {/* Password Requirements */}
        <View style={styles.requirementsSection}>
          <Text style={styles.requirementsTitle}>Password Requirements</Text>
          <View style={styles.requirementsList}>
            <RequirementItem 
              text="At least 8 characters" 
              met={password.length >= 8} 
            />
            <RequirementItem 
              text="Contains uppercase letter" 
              met={/[A-Z]/.test(password)} 
            />
            <RequirementItem 
              text="Contains lowercase letter" 
              met={/[a-z]/.test(password)} 
            />
            <RequirementItem 
              text="Contains number" 
              met={/\d/.test(password)} 
            />
            <RequirementItem 
              text="Contains special character" 
              met={/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)} 
            />
          </View>
        </View>

        {/* Password Input */}
        <View style={styles.passwordSection}>
          <Input
            label="Master Password"
            placeholder="Create a strong master password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!showPassword}
            rightIcon={showPassword ? <EyeOff size={20} color="#6b7280" /> : <Eye size={20} color="#6b7280" />}
            onRightIconPress={() => setShowPassword(!showPassword)}
            autoCapitalize="none"
            autoCorrect={false}
          />

          {password.length > 0 && (
            <View style={styles.strengthContainer}>
              <View style={styles.strengthBar}>
                <View 
                  style={[
                    styles.strengthFill, 
                    { 
                      width: `${strength.progress}%`,
                      backgroundColor: strength.color 
                    }
                  ]} 
                />
              </View>
              <Text style={[styles.strengthText, { color: strength.color }]}>
                {strength.label}
              </Text>
            </View>
          )}

          <Input
            label="Confirm Password"
            placeholder="Confirm your master password"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry={!showConfirmPassword}
            rightIcon={showConfirmPassword ? <EyeOff size={20} color="#6b7280" /> : <Eye size={20} color="#6b7280" />}
            onRightIconPress={() => setShowConfirmPassword(!showConfirmPassword)}
            autoCapitalize="none"
            autoCorrect={false}
          />
        </View>

        {/* Security Notice */}
        <View style={styles.securityNotice}>
          <Text style={styles.noticeTitle}>🔒 Security Notice</Text>
          <Text style={styles.noticeText}>
            Your master password cannot be recovered if forgotten. Make sure to remember it or store it safely.
          </Text>
        </View>

        {/* Create Button */}
        <Button
          onPress={handleSetup}
          loading={isLoading}
          disabled={!password || !confirmPassword || password !== confirmPassword || isLoading}
          fullWidth
        >
          Create Secure Vault
        </Button>
      </ScrollView>
    </SafeAreaView>
  );
}

interface RequirementItemProps {
  text: string;
  met: boolean;
}

function RequirementItem({ text, met }: RequirementItemProps) {
  return (
    <View style={styles.requirementItem}>
      <View style={[styles.requirementIcon, { backgroundColor: met ? '#dcfce7' : '#f1f5f9' }]}>
        <Check size={12} color={met ? '#16a34a' : '#94a3b8'} />
      </View>
      <Text style={[styles.requirementText, { color: met ? '#16a34a' : '#64748b' }]}>
        {text}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  iconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    backgroundColor: '#eff6ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#dbeafe',
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
  requirementsSection: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  requirementsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
  },
  requirementsList: {
    gap: 12,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  requirementIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  requirementText: {
    fontSize: 14,
    fontWeight: '500',
  },
  passwordSection: {
    gap: 20,
    marginBottom: 24,
  },
  strengthContainer: {
    marginTop: -12,
  },
  strengthBar: {
    height: 4,
    backgroundColor: '#e2e8f0',
    borderRadius: 2,
    marginBottom: 8,
  },
  strengthFill: {
    height: '100%',
    borderRadius: 2,
  },
  strengthText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'right',
  },
  securityNotice: {
    backgroundColor: '#fef3c7',
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: '#fde68a',
  },
  noticeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#92400e',
    marginBottom: 8,
  },
  noticeText: {
    fontSize: 14,
    color: '#92400e',
    lineHeight: 20,
  },
});