import { CRYPTO_CONFIG } from '../../config/security';
import { KeyDerivationParams, DerivedKey, CryptoError } from '../../types/crypto';

/**
 * Key derivation using Argon2id (via PBKDF2 as fallback)
 * Note: True Argon2id would require a native implementation
 */
export class KeyDerivationService {
  private static readonly config = CRYPTO_CONFIG.argon2;

  /**
   * Derive encryption key from password and salt
   */
  static async deriveKey(params: KeyDerivationParams): Promise<DerivedKey> {
    try {
      const {
        password,
        salt,
        iterations = this.config.timeCost * 100000, // Scale for PBKDF2
        keyLength = this.config.hashLength,
      } = params;

      // Import password as key material
      const keyMaterial = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(password),
        { name: 'PBKDF2' },
        false,
        ['deriveBits']
      );

      // Derive key using PBKDF2 (as Argon2id fallback)
      const derivedBits = await crypto.subtle.deriveBits(
        {
          name: 'PBKDF2',
          salt: salt,
          iterations: iterations,
          hash: 'SHA-256',
        },
        keyMaterial,
        keyLength * 8 // Convert to bits
      );

      // Import derived bits as AES key
      const derivedKey = await crypto.subtle.importKey(
        'raw',
        derivedBits,
        { name: 'AES-GCM' },
        false, // Not extractable for security
        ['encrypt', 'decrypt']
      );

      return {
        key: derivedKey,
        salt: salt,
        iterations: iterations,
      };
    } catch (error) {
      throw new CryptoError(
        `Key derivation failed: ${error.message}`,
        'KEY_DERIVATION_FAILED',
        'critical'
      );
    }
  }

  /**
   * Generate cryptographically secure salt
   */
  static generateSalt(): Uint8Array {
    return crypto.getRandomValues(new Uint8Array(CRYPTO_CONFIG.salt.length));
  }

  /**
   * Derive key specifically for vault encryption
   */
  static async deriveVaultKey(password: string, salt: Uint8Array): Promise<CryptoKey> {
    const result = await this.deriveKey({
      password,
      salt,
      iterations: this.config.timeCost * 100000,
      keyLength: this.config.hashLength,
    });
    
    return result.key;
  }

  /**
   * Derive key for export/import operations
   */
  static async deriveExportKey(password: string, salt: Uint8Array, iterations?: number): Promise<CryptoKey> {
    const result = await this.deriveKey({
      password,
      salt,
      iterations: iterations || 100000, // Standard PBKDF2 iterations for export
      keyLength: 32, // 256-bit key
    });
    
    return result.key;
  }

  /**
   * Calculate optimal iteration count based on device performance
   */
  static async calculateOptimalIterations(targetTimeMs: number = 500): Promise<number> {
    const testPassword = 'test-password-for-calibration';
    const testSalt = this.generateSalt();
    const baseIterations = 10000;
    
    try {
      const startTime = performance.now();
      await this.deriveKey({
        password: testPassword,
        salt: testSalt,
        iterations: baseIterations,
      });
      const duration = performance.now() - startTime;
      
      // Scale iterations to achieve target time
      const scaleFactor = targetTimeMs / duration;
      const optimalIterations = Math.max(
        baseIterations,
        Math.floor(baseIterations * scaleFactor)
      );
      
      return Math.min(optimalIterations, 1000000); // Cap at 1M iterations
    } catch {
      // Return safe default if calibration fails
      return this.config.timeCost * 100000;
    }
  }

  /**
   * Verify password against derived key (constant-time comparison)
   */
  static async verifyPassword(
    password: string,
    salt: Uint8Array,
    expectedKey: CryptoKey,
    iterations: number
  ): Promise<boolean> {
    try {
      const derivedResult = await this.deriveKey({
        password,
        salt,
        iterations,
      });

      // Since we can't directly compare CryptoKeys, we'll encrypt a test value
      const testData = 'verification-test';
      const testIV = crypto.getRandomValues(new Uint8Array(12));
      
      try {
        // Try to encrypt with expected key
        await crypto.subtle.encrypt(
          { name: 'AES-GCM', iv: testIV },
          expectedKey,
          new TextEncoder().encode(testData)
        );
        
        // Try to encrypt with derived key
        await crypto.subtle.encrypt(
          { name: 'AES-GCM', iv: testIV },
          derivedResult.key,
          new TextEncoder().encode(testData)
        );
        
        return true; // Both succeed, assume keys match
      } catch {
        return false; // Encryption failed, keys don't match
      }
    } catch {
      return false;
    }
  }
}

/**
 * Custom CryptoError class
 */
class CryptoError extends Error {
  constructor(
    message: string,
    public code: string,
    public severity: 'low' | 'medium' | 'high' | 'critical'
  ) {
    super(message);
    this.name = 'CryptoError';
  }
}

// Convenience exports
export const deriveVaultKey = KeyDerivationService.deriveVaultKey;
export const deriveExportKey = KeyDerivationService.deriveExportKey;
export const generateSalt = KeyDerivationService.generateSalt;