import { TIMING } from './constants';

/**
 * Format time duration in human-readable format
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m`;
  } else if (seconds < 86400) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  } else {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    return hours > 0 ? `${days}d ${hours}h` : `${days}d`;
  }
};

/**
 * Format relative time (e.g., "2 minutes ago")
 */
export const formatRelativeTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = Math.floor((now - timestamp) / 1000);

  if (diff < 60) {
    return 'Just now';
  } else if (diff < 3600) {
    const minutes = Math.floor(diff / 60);
    return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
  } else if (diff < 86400) {
    const hours = Math.floor(diff / 3600);
    return `${hours} hour${hours === 1 ? '' : 's'} ago`;
  } else if (diff < 604800) {
    const days = Math.floor(diff / 86400);
    return `${days} day${days === 1 ? '' : 's'} ago`;
  } else {
    return new Date(timestamp).toLocaleDateString();
  }
};

/**
 * Format file size in human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
};

/**
 * Format password strength as text
 */
export const formatPasswordStrength = (score: number): { label: string; color: string } => {
  switch (score) {
    case 0:
    case 1:
      return { label: 'Very Weak', color: '#ef4444' };
    case 2:
      return { label: 'Weak', color: '#f97316' };
    case 3:
      return { label: 'Fair', color: '#eab308' };
    case 4:
      return { label: 'Good', color: '#22c55e' };
    case 5:
      return { label: 'Strong', color: '#16a34a' };
    default:
      return { label: 'Unknown', color: '#6b7280' };
  }
};

/**
 * Mask sensitive text (e.g., passwords)
 */
export const maskText = (text: string, visibleChars: number = 0): string => {
  if (visibleChars === 0) {
    return '•'.repeat(Math.min(text.length, 12));
  }
  
  if (text.length <= visibleChars * 2) {
    return text;
  }
  
  const start = text.substring(0, visibleChars);
  const end = text.substring(text.length - visibleChars);
  const middle = '•'.repeat(Math.min(text.length - visibleChars * 2, 8));
  
  return `${start}${middle}${end}`;
};

/**
 * Format TOTP time remaining
 */
export const formatTotpTimeRemaining = (timeRemaining: number): string => {
  return `${timeRemaining}s`;
};

/**
 * Format TOTP code with spacing
 */
export const formatTotpCode = (code: string): string => {
  if (code.length === 6) {
    return `${code.substring(0, 3)} ${code.substring(3)}`;
  } else if (code.length === 8) {
    return `${code.substring(0, 4)} ${code.substring(4)}`;
  }
  return code;
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return `${text.substring(0, maxLength - 3)}...`;
};

/**
 * Format URL for display (remove protocol, www)
 */
export const formatUrlForDisplay = (url: string): string => {
  try {
    const urlObj = new URL(url);
    let hostname = urlObj.hostname;
    
    // Remove www prefix
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    return hostname;
  } catch {
    return url;
  }
};

/**
 * Format entry type for display
 */
export const formatEntryType = (type: string): string => {
  switch (type) {
    case 'password':
      return 'Password';
    case 'totp':
      return '2FA';
    case 'note':
      return 'Note';
    default:
      return type.charAt(0).toUpperCase() + type.slice(1);
  }
};

/**
 * Format sync status
 */
export const formatSyncStatus = (status: string, lastSync?: number): string => {
  switch (status) {
    case 'synced':
      return lastSync ? `Synced ${formatRelativeTime(lastSync)}` : 'Synced';
    case 'syncing':
      return 'Syncing...';
    case 'conflict':
      return 'Sync conflict';
    case 'error':
      return 'Sync failed';
    case 'offline':
      return 'Offline';
    default:
      return 'Unknown';
  }
};

/**
 * Format search query highlighting
 */
export const highlightSearchQuery = (text: string, query: string): string => {
  if (!query.trim()) {
    return text;
  }
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
};

/**
 * Format number with commas
 */
export const formatNumber = (num: number): string => {
  return num.toLocaleString();
};

/**
 * Format percentage
 */
export const formatPercentage = (value: number, total: number): string => {
  if (total === 0) return '0%';
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(1)}%`;
};

/**
 * Format date for display
 */
export const formatDate = (timestamp: number, includeTime: boolean = false): string => {
  const date = new Date(timestamp);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  
  if (includeTime) {
    options.hour = '2-digit';
    options.minute = '2-digit';
  }
  
  return date.toLocaleDateString(undefined, options);
};

/**
 * Format currency (for premium features)
 */
export const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
};

/**
 * Capitalize first letter
 */
export const capitalize = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

/**
 * Convert camelCase to Title Case
 */
export const camelToTitle = (text: string): string => {
  return text
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
};

/**
 * Format validation errors for display
 */
export const formatValidationErrors = (errors: string[]): string => {
  if (errors.length === 0) return '';
  if (errors.length === 1) return errors[0];
  
  return errors.map((error, index) => `${index + 1}. ${error}`).join('\n');
};

/**
 * Format device info for display
 */
export const formatDeviceInfo = (platform: string, version?: string): string => {
  const platformName = capitalize(platform);
  return version ? `${platformName} ${version}` : platformName;
};