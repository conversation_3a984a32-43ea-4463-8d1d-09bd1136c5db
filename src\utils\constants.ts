// Application constants

// Timing constants
export const TIMING = {
  AUTO_LOCK_MIN: 30, // 30 seconds
  AUTO_LOCK_MAX: 3600, // 1 hour
  AUTO_LOCK_DEFAULT: 300, // 5 minutes
  BIOMETRIC_TIMEOUT: 30000, // 30 seconds
  SYNC_TIMEOUT: 30000, // 30 seconds
  SEARCH_DEBOUNCE: 300, // 300ms
  TOAST_DURATION: 3000, // 3 seconds
  ANIMATION_FAST: 150,
  ANIMATION_NORMAL: 250,
  ANIMATION_SLOW: 350,
} as const;

// Size limits
export const LIMITS = {
  MAX_VAULT_ENTRIES: 10000,
  MAX_ENTRY_TITLE_LENGTH: 100,
  MAX_USERNAME_LENGTH: 100,
  MAX_PASSWORD_LENGTH: 1000,
  MAX_URL_LENGTH: 2000,
  MAX_NOTES_LENGTH: 10000,
  MAX_CUSTOM_FIELDS: 20,
  MAX_FIELD_NAME_LENGTH: 50,
  MAX_FIELD_VALUE_LENGTH: 1000,
  MAX_TAGS: 10,
  MAX_TAG_LENGTH: 30,
  MAX_SEARCH_RESULTS: 100,
  MAX_BACKUP_SIZE: 50 * 1024 * 1024, // 50MB
} as const;

// Password generation
export const PASSWORD_GENERATION = {
  MIN_LENGTH: 4,
  MAX_LENGTH: 128,
  DEFAULT_LENGTH: 16,
  UPPERCASE_CHARS: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  LOWERCASE_CHARS: 'abcdefghijklmnopqrstuvwxyz',
  NUMBER_CHARS: '0123456789',
  SYMBOL_CHARS: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  SIMILAR_CHARS: 'il1Lo0O',
  AMBIGUOUS_CHARS: '{}[]()/\\\'"`~,;.<>',
} as const;

// TOTP constants
export const TOTP = {
  DEFAULT_PERIOD: 30, // seconds
  DEFAULT_DIGITS: 6,
  DEFAULT_ALGORITHM: 'SHA1',
  SUPPORTED_ALGORITHMS: ['SHA1', 'SHA256', 'SHA512'],
  MIN_PERIOD: 15,
  MAX_PERIOD: 300,
  MIN_DIGITS: 6,
  MAX_DIGITS: 8,
} as const;

// File extensions and MIME types
export const FILE_TYPES = {
  VAULT_EXPORT: '.twofai',
  JSON_EXPORT: '.json',
  CSV_EXPORT: '.csv',
  BACKUP_EXTENSION: '.backup',
  MIME_TYPES: {
    JSON: 'application/json',
    CSV: 'text/csv',
    OCTET_STREAM: 'application/octet-stream',
  },
} as const;

// Regular expressions
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/.+/,
  STRONG_PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  TOTP_SECRET: /^[A-Z2-7]+=*$/,
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
} as const;

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  AUTHENTICATION_FAILED: 'Authentication failed. Please check your credentials.',
  VAULT_LOCKED: 'Vault is locked. Please unlock to continue.',
  INVALID_PASSWORD: 'Invalid password. Please try again.',
  BIOMETRIC_FAILED: 'Biometric authentication failed.',
  SYNC_FAILED: 'Synchronization failed. Changes saved locally.',
  STORAGE_FULL: 'Storage is full. Please free up space.',
  INVALID_FORMAT: 'Invalid file format.',
  CORRUPTED_DATA: 'Data appears to be corrupted.',
  PERMISSION_DENIED: 'Permission denied.',
  UNKNOWN_ERROR: 'An unknown error occurred.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  VAULT_UNLOCKED: 'Vault unlocked successfully',
  ENTRY_SAVED: 'Entry saved successfully',
  ENTRY_DELETED: 'Entry deleted successfully',
  PASSWORD_COPIED: 'Password copied to clipboard',
  TOTP_COPIED: 'TOTP code copied to clipboard',
  SYNC_COMPLETED: 'Synchronization completed',
  BACKUP_CREATED: 'Backup created successfully',
  SETTINGS_SAVED: 'Settings saved successfully',
  BIOMETRIC_ENABLED: 'Biometric authentication enabled',
} as const;

// Storage keys
export const STORAGE_KEYS = {
  VAULT_DATA: 'vault_data',
  USER_PREFERENCES: 'user_preferences',
  BIOMETRIC_KEY: 'biometric_key',
  DEVICE_ID: 'device_id',
  LAST_SYNC: 'last_sync',
  OFFLINE_QUEUE: 'offline_queue',
  SECURITY_LOG: 'security_log',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  VAULTS: '/vaults',
  SYNC: '/sync',
  AUTH: '/auth',
  USER: '/user',
  PREFERENCES: '/preferences',
  HEALTH: '/health',
} as const;

// Theme constants
export const THEME_CONSTANTS = {
  LIGHT_THEME: 'light',
  DARK_THEME: 'dark',
  SYSTEM_THEME: 'system',
  THEME_STORAGE_KEY: 'theme_preference',
} as const;

// Platform constants
export const PLATFORM = {
  IOS: 'ios',
  ANDROID: 'android',
  WEB: 'web',
  WINDOWS: 'windows',
  MACOS: 'macos',
  LINUX: 'linux',
} as const;

// Security levels
export const SECURITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const;

// Sync states
export const SYNC_STATES = {
  IDLE: 'idle',
  SYNCING: 'syncing',
  CONFLICT: 'conflict',
  ERROR: 'error',
  OFFLINE: 'offline',
} as const;

// Entry types
export const ENTRY_TYPES = {
  PASSWORD: 'password',
  TOTP: 'totp',
  NOTE: 'note',
} as const;

// Biometric types
export const BIOMETRIC_TYPES = {
  FINGERPRINT: 'fingerprint',
  FACE: 'face',
  IRIS: 'iris',
  VOICE: 'voice',
} as const;