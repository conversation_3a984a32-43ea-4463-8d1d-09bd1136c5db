import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Switch, ScrollView, Platform } from 'react-native';
import { RefreshCw, Copy, Settings, Check } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import * as Clipboard from 'expo-clipboard';

interface PasswordGeneratorProps {
  onPasswordGenerated?: (password: string) => void;
  initialLength?: number;
}

interface GeneratorOptions {
  length: number;
  includeUppercase: boolean;
  includeLowercase: boolean;
  includeNumbers: boolean;
  includeSymbols: boolean;
  excludeSimilar: boolean;
  excludeAmbiguous: boolean;
}

export default function PasswordGenerator({
  onPasswordGenerated,
  initialLength = 16,
}: PasswordGeneratorProps) {
  const [password, setPassword] = useState('');
  const [copied, setCopied] = useState(false);
  const [options, setOptions] = useState<GeneratorOptions>({
    length: initialLength,
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSymbols: true,
    excludeSimilar: false,
    excludeAmbiguous: false,
  });
  const [showAdvanced, setShowAdvanced] = useState(false);

  const generatePassword = useCallback(() => {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    const similar = 'il1Lo0O';
    const ambiguous = '{}[]()/\\\'"`~,;.<>';

    let charset = '';
    
    if (options.includeUppercase) charset += uppercase;
    if (options.includeLowercase) charset += lowercase;
    if (options.includeNumbers) charset += numbers;
    if (options.includeSymbols) charset += symbols;

    if (options.excludeSimilar) {
      charset = charset.split('').filter(char => !similar.includes(char)).join('');
    }
    
    if (options.excludeAmbiguous) {
      charset = charset.split('').filter(char => !ambiguous.includes(char)).join('');
    }

    if (charset.length === 0) {
      setPassword('Error: No character types selected');
      return;
    }

    let result = '';
    for (let i = 0; i < options.length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    setPassword(result);
    onPasswordGenerated?.(result);
    setCopied(false);
  }, [options, onPasswordGenerated]);

  const getPasswordStrength = (pwd: string) => {
    let score = 0;
    if (pwd.length >= 8) score++;
    if (pwd.length >= 12) score++;
    if (/[A-Z]/.test(pwd)) score++;
    if (/[a-z]/.test(pwd)) score++;
    if (/\d/.test(pwd)) score++;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd)) score++;

    if (score <= 2) return { label: 'Weak', color: '#ef4444' };
    if (score <= 3) return { label: 'Fair', color: '#f59e0b' };
    if (score <= 4) return { label: 'Good', color: '#10b981' };
    return { label: 'Strong', color: '#059669' };
  };

  const updateOption = (key: keyof GeneratorOptions, value: boolean | number) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const copyToClipboard = async () => {
    if (password && password !== 'Error: No character types selected') {
      await Clipboard.setStringAsync(password);
      setCopied(true);
      
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
      
      setTimeout(() => setCopied(false), 2000);
    }
  };

  useEffect(() => {
    generatePassword();
  }, [generatePassword]);

  const strength = getPasswordStrength(password);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Generated Password */}
      <View style={styles.passwordSection}>
        <View style={styles.passwordHeader}>
          <Text style={styles.sectionTitle}>Generated Password</Text>
          <View style={styles.strengthIndicator}>
            <View style={[styles.strengthDot, { backgroundColor: strength.color }]} />
            <Text style={[styles.strengthText, { color: strength.color }]}>
              {strength.label}
            </Text>
          </View>
        </View>

        <View style={styles.passwordContainer}>
          <Text style={styles.passwordText} selectable>
            {password}
          </Text>
        </View>

        <View style={styles.passwordActions}>
          <TouchableOpacity style={styles.actionButton} onPress={generatePassword}>
            <RefreshCw size={16} color="#3b82f6" />
            <Text style={styles.actionButtonText}>Generate</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={copyToClipboard}>
            {copied ? (
              <Check size={16} color="#10b981" />
            ) : (
              <Copy size={16} color="#3b82f6" />
            )}
            <Text style={[styles.actionButtonText, copied && { color: '#10b981' }]}>
              {copied ? 'Copied!' : 'Copy'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Length Control */}
      <View style={styles.section}>
        <View style={styles.lengthHeader}>
          <Text style={styles.sectionTitle}>Length</Text>
          <Text style={styles.lengthValue}>{options.length}</Text>
        </View>

        <View style={styles.lengthControls}>
          <TouchableOpacity
            style={styles.lengthButton}
            onPress={() => updateOption('length', Math.max(4, options.length - 1))}
          >
            <Text style={styles.lengthButtonText}>-</Text>
          </TouchableOpacity>

          <View style={styles.lengthSlider}>
            <View style={styles.sliderTrack} />
            <View 
              style={[
                styles.sliderFill, 
                { width: `${((options.length - 4) / 124) * 100}%` }
              ]} 
            />
          </View>

          <TouchableOpacity
            style={styles.lengthButton}
            onPress={() => updateOption('length', Math.min(128, options.length + 1))}
          >
            <Text style={styles.lengthButtonText}>+</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Character Types */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Character Types</Text>

        <View style={styles.optionsList}>
          <View style={styles.option}>
            <View style={styles.optionInfo}>
              <Text style={styles.optionTitle}>Uppercase Letters</Text>
              <Text style={styles.optionDescription}>A-Z</Text>
            </View>
            <Switch
              value={options.includeUppercase}
              onValueChange={(value) => updateOption('includeUppercase', value)}
              trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>

          <View style={styles.option}>
            <View style={styles.optionInfo}>
              <Text style={styles.optionTitle}>Lowercase Letters</Text>
              <Text style={styles.optionDescription}>a-z</Text>
            </View>
            <Switch
              value={options.includeLowercase}
              onValueChange={(value) => updateOption('includeLowercase', value)}
              trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>

          <View style={styles.option}>
            <View style={styles.optionInfo}>
              <Text style={styles.optionTitle}>Numbers</Text>
              <Text style={styles.optionDescription}>0-9</Text>
            </View>
            <Switch
              value={options.includeNumbers}
              onValueChange={(value) => updateOption('includeNumbers', value)}
              trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>

          <View style={styles.option}>
            <View style={styles.optionInfo}>
              <Text style={styles.optionTitle}>Symbols</Text>
              <Text style={styles.optionDescription}>!@#$%^&*</Text>
            </View>
            <Switch
              value={options.includeSymbols}
              onValueChange={(value) => updateOption('includeSymbols', value)}
              trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
              thumbColor="#ffffff"
            />
          </View>
        </View>
      </View>

      {/* Advanced Options */}
      <View style={styles.section}>
        <TouchableOpacity
          style={styles.advancedToggle}
          onPress={() => setShowAdvanced(!showAdvanced)}
        >
          <Text style={styles.sectionTitle}>Advanced Options</Text>
          <Settings size={16} color="#6b7280" />
        </TouchableOpacity>

        {showAdvanced && (
          <View style={styles.optionsList}>
            <View style={styles.option}>
              <View style={styles.optionInfo}>
                <Text style={styles.optionTitle}>Exclude Similar Characters</Text>
                <Text style={styles.optionDescription}>Avoid i, l, 1, L, o, 0, O</Text>
              </View>
              <Switch
                value={options.excludeSimilar}
                onValueChange={(value) => updateOption('excludeSimilar', value)}
                trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
                thumbColor="#ffffff"
              />
            </View>

            <View style={styles.option}>
              <View style={styles.optionInfo}>
                <Text style={styles.optionTitle}>Exclude Ambiguous Characters</Text>
                <Text style={styles.optionDescription}>Avoid {}, [], (), /, \, ', ", `</Text>
              </View>
              <Switch
                value={options.excludeAmbiguous}
                onValueChange={(value) => updateOption('excludeAmbiguous', value)}
                trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
                thumbColor="#ffffff"
              />
            </View>
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  passwordSection: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    margin: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  passwordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  strengthIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  strengthDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  strengthText: {
    fontSize: 14,
    fontWeight: '500',
  },
  passwordContainer: {
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    minHeight: 80,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  passwordText: {
    fontSize: 20,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    color: '#1e293b',
    textAlign: 'center',
    letterSpacing: 1,
    lineHeight: 28,
  },
  passwordActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 14,
    paddingHorizontal: 20,
    backgroundColor: '#f1f5f9',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#3b82f6',
  },
  section: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  lengthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  lengthValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#3b82f6',
  },
  lengthControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  lengthButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#f1f5f9',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  lengthButtonText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#475569',
  },
  lengthSlider: {
    flex: 1,
    height: 20,
    justifyContent: 'center',
  },
  sliderTrack: {
    height: 6,
    backgroundColor: '#e2e8f0',
    borderRadius: 3,
  },
  sliderFill: {
    position: 'absolute',
    height: 6,
    backgroundColor: '#3b82f6',
    borderRadius: 3,
  },
  advancedToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  optionsList: {
    gap: 20,
  },
  option: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionInfo: {
    flex: 1,
    marginRight: 16,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    color: '#6b7280',
  },
});