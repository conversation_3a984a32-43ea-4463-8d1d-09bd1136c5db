import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { Lock, Key, FileText, Co<PERSON>, Eye, EyeOff, Star } from 'lucide-react-native';

interface VaultEntryProps {
  id: string;
  type: 'password' | 'totp' | 'note';
  title: string;
  subtitle?: string;
  lastUsed?: Date;
  isFavorite?: boolean;
  onPress: () => void;
  onCopy?: () => void;
  showSensitive?: boolean;
  onToggleSensitive?: () => void;
}

export default function VaultEntry({
  id,
  type,
  title,
  subtitle,
  lastUsed,
  isFavorite,
  onPress,
  onCopy,
  showSensitive = false,
  onToggleSensitive,
}: VaultEntryProps) {
  const getTypeIcon = () => {
    const iconProps = { size: 20 };
    switch (type) {
      case 'password':
        return <Lock {...iconProps} color="#3b82f6" />;
      case 'totp':
        return <Key {...iconProps} color="#10b981" />;
      case 'note':
        return <FileText {...iconProps} color="#f59e0b" />;
      default:
        return <Lock {...iconProps} color="#6b7280" />;
    }
  };

  const getTypeLabel = () => {
    switch (type) {
      case 'password':
        return 'Password';
      case 'totp':
        return '2FA';
      case 'note':
        return 'Note';
      default:
        return 'Unknown';
    }
  };

  const getTypeColor = () => {
    switch (type) {
      case 'password':
        return '#eff6ff';
      case 'totp':
        return '#ecfdf5';
      case 'note':
        return '#fffbeb';
      default:
        return '#f8fafc';
    }
  };

  const formatLastUsed = (date?: Date) => {
    if (!date) return 'Never used';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.7}>
      <View style={styles.content}>
        {/* Icon and Type */}
        <View style={[styles.iconContainer, { backgroundColor: getTypeColor() }]}>
          {getTypeIcon()}
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          <View style={styles.titleRow}>
            <Text style={styles.title} numberOfLines={1}>
              {title}
            </Text>
            {isFavorite && (
              <View style={styles.favoriteIndicator}>
                <Star size={14} color="#f59e0b" fill="#f59e0b" />
              </View>
            )}
          </View>
          
          {subtitle && (
            <Text style={styles.subtitle} numberOfLines={1}>
              {showSensitive ? subtitle : '••••••••••••'}
            </Text>
          )}
          
          <View style={styles.metaRow}>
            <Text style={styles.typeLabel}>{getTypeLabel()}</Text>
            <Text style={styles.lastUsed}>{formatLastUsed(lastUsed)}</Text>
          </View>
        </View>

        {/* Actions */}
        <View style={styles.actions}>
          {onToggleSensitive && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={onToggleSensitive}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              {showSensitive ? (
                <EyeOff size={18} color="#6b7280" />
              ) : (
                <Eye size={18} color="#6b7280" />
              )}
            </TouchableOpacity>
          )}
          
          {onCopy && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={onCopy}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Copy size={18} color="#6b7280" />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 6,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#f1f5f9',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  mainContent: {
    flex: 1,
    minHeight: 48,
    justifyContent: 'center',
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  title: {
    fontSize: 17,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
  },
  favoriteIndicator: {
    marginLeft: 8,
  },
  subtitle: {
    fontSize: 15,
    color: '#6b7280',
    marginBottom: 6,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  typeLabel: {
    fontSize: 12,
    color: '#9ca3af',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  lastUsed: {
    fontSize: 12,
    color: '#9ca3af',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 10,
    borderRadius: 10,
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
});