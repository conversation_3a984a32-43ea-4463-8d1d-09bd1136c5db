# Platform Storage Architecture

## Overview

TWOFAI implements secure local storage across iOS, Android, and Web platforms using platform-specific secure storage mechanisms. All vault data is encrypted before storage, and sensitive keys are protected by device hardware security modules where available.

## Storage Hierarchy

### Primary Storage (Encrypted Vault)
- **Purpose**: Store encrypted vault blob locally
- **Location**: Platform-specific secure document directory
- **Encryption**: AES-256-GCM with user-derived key
- **Backup**: Excluded from automatic cloud backups

### Keychain/SecureStore (Biometric Keys)
- **Purpose**: Store device-bound encryption keys for biometric unlock
- **Protection**: Hardware-backed security (TEE/Secure Enclave)
- **Access**: Biometric authentication required
- **Fallback**: Master password derivation

### Preferences Storage (Settings)
- **Purpose**: Store non-sensitive user preferences
- **Location**: Platform-specific app preferences
- **Encryption**: Not required (non-sensitive data only)
- **Backup**: Included in app backup

## iOS Implementation

### File System Storage
```typescript
import * as FileSystem from 'expo-file-system';

const IOS_STORAGE_CONFIG = {
  vaultDirectory: `${FileSystem.documentDirectory}vault/`,
  vaultFile: 'encrypted_vault.dat',
  backupExcluded: true, // Prevent iCloud backup
  protection: FileSystem.FileSystemSessionType.BACKGROUND_AND_FOREGROUND
};
```

### Keychain Services
```typescript
import * as SecureStore from 'expo-secure-store';

const IOS_KEYCHAIN_CONFIG = {
  keychainService: 'com.twofai.biometric-key',
  accessGroup: undefined, // App-specific
  requireAuthentication: true,
  authenticatePrompt: 'Unlock TWOFAI with biometrics',
  authenticateCancel: 'Cancel',
  authenticateFallback: 'Use Master Password'
};
```

### Secure Enclave Integration
```typescript
// Biometric key storage with Secure Enclave
const storeBiometricKey = async (keyData: string): Promise<void> => {
  await SecureStore.setItemAsync('biometric_vault_key', keyData, {
    requireAuthentication: true,
    keychainService: IOS_KEYCHAIN_CONFIG.keychainService,
    touchID: true,
    faceID: true,
    showModal: true,
    kSecAccessControl: 'kSecAccessControlBiometryAny'
  });
};
```

### File Protection Classes
```typescript
// Set file protection level
const setFileProtection = async (filePath: string): Promise<void> => {
  // Complete protection when locked
  await FileSystem.setItemAsync(filePath, {
    protection: FileSystem.FileProtection.COMPLETE_UNLESS_OPEN
  });
};
```

## Android Implementation

### Internal Storage
```typescript
const ANDROID_STORAGE_CONFIG = {
  vaultDirectory: `${FileSystem.documentDirectory}vault/`,
  vaultFile: 'encrypted_vault.dat',
  mode: 'MODE_PRIVATE', // App-private access only
  excludeFromBackup: true
};
```

### Android Keystore
```typescript
// Hardware-backed key storage
const ANDROID_KEYSTORE_CONFIG = {
  keyAlias: 'twofai_biometric_key',
  keyStore: 'AndroidKeyStore',
  requiresAuthentication: true,
  authenticationValidityDuration: 30, // seconds
  userAuthenticationRequired: true,
  invalidatedByBiometricEnrollment: true
};
```

### Hardware Security Module
```typescript
// Generate hardware-backed key
const generateHardwareKey = async (): Promise<string> => {
  return await SecureStore.setItemAsync('biometric_vault_key', randomKeyData, {
    requireAuthentication: true,
    authenticationPrompt: 'Authenticate to unlock TWOFAI',
    authenticationCancel: 'Cancel',
    authenticationFallback: 'Use Password'
  });
};
```

### File Encryption (Android 7+)
```typescript
// Use Android file-based encryption
const ANDROID_ENCRYPTION_CONFIG = {
  directBootAware: false, // Require device unlock
  encryptionRequired: true,
  keyDerivation: 'PBKDF2WithHmacSHA256'
};
```

## Web Platform Implementation

### IndexedDB Storage
```typescript
const WEB_STORAGE_CONFIG = {
  databaseName: 'TWOFAIVault',
  objectStoreName: 'encrypted_vaults',
  version: 1,
  keyPath: 'id'
};

// IndexedDB vault storage
const storeVaultWeb = async (encryptedVault: EncryptedVault): Promise<void> => {
  const db = await openIndexedDB();
  const transaction = db.transaction(['encrypted_vaults'], 'readwrite');
  const store = transaction.objectStore('encrypted_vaults');
  await store.put(encryptedVault);
};
```

### Web Crypto API
```typescript
// Browser-based key derivation and storage
const WEB_CRYPTO_CONFIG = {
  keyDerivation: {
    name: 'PBKDF2',
    hash: 'SHA-256',
    iterations: 100000, // Adjusted for web performance
    salt: new Uint8Array(32)
  },
  encryption: {
    name: 'AES-GCM',
    length: 256,
    iv: new Uint8Array(12)
  }
};
```

### Local Storage (Preferences Only)
```typescript
// Non-sensitive preferences in localStorage
const WEB_PREFERENCES_CONFIG = {
  prefix: 'twofai_',
  encryption: false, // Non-sensitive data only
  compression: true
};
```

### Service Worker Caching
```typescript
// Offline-first app shell caching
const WEB_CACHE_CONFIG = {
  cacheName: 'twofai-v1',
  cacheStrategy: 'NetworkFirst',
  maxAge: 86400, // 24 hours
  excludeVaultData: true // Never cache sensitive data
};
```

## Cross-Platform Storage Manager

### Unified Storage Interface
```typescript
interface PlatformStorage {
  // Vault operations
  saveVault(vault: EncryptedVault): Promise<void>;
  loadVault(): Promise<EncryptedVault | null>;
  deleteVault(): Promise<void>;
  
  // Biometric key operations
  saveBiometricKey(key: string): Promise<void>;
  loadBiometricKey(): Promise<string | null>;
  deleteBiometricKey(): Promise<void>;
  
  // Preferences
  savePreferences(prefs: UserPreferences): Promise<void>;
  loadPreferences(): Promise<UserPreferences>;
  
  // Platform capabilities
  supportsBiometrics(): Promise<boolean>;
  supportsHardwareBackedKeys(): Promise<boolean>;
}
```

### Platform Detection
```typescript
import { Platform } from 'react-native';

const createPlatformStorage = (): PlatformStorage => {
  switch (Platform.OS) {
    case 'ios':
      return new IOSStorage();
    case 'android':
      return new AndroidStorage();
    case 'web':
      return new WebStorage();
    default:
      throw new Error(`Unsupported platform: ${Platform.OS}`);
  }
};
```

## Security Considerations

### File System Security
- Use platform-specific secure directories
- Exclude vault files from automatic backups
- Set appropriate file permissions and protection classes
- Implement secure file deletion (overwrite with random data)

### Key Management
- Never store encryption keys in plain text
- Use hardware-backed key storage where available
- Implement key rotation for long-term security
- Secure key derivation with platform-specific APIs

### Memory Protection
- Clear sensitive data from memory after use
- Use secure string handling where supported
- Monitor for memory dumps and debugging attempts
- Implement anti-tampering measures

## Storage Migration

### Version Management
```typescript
interface StorageVersion {
  version: number;
  migrationRequired: boolean;
  backwardCompatible: boolean;
}

const STORAGE_VERSIONS: StorageVersion[] = [
  { version: 1, migrationRequired: false, backwardCompatible: true },
  { version: 2, migrationRequired: true, backwardCompatible: true }
];
```

### Migration Strategy
1. Detect storage version on app launch
2. Create backup of existing vault before migration
3. Apply migration transformations
4. Verify migrated data integrity
5. Clean up old data after successful migration

## Performance Optimization

### Caching Strategy
- Cache decrypted vault in memory during active session
- Implement LRU cache for frequently accessed entries
- Preload critical data on app launch
- Lazy load non-essential vault content

### I/O Optimization
- Batch read/write operations where possible
- Use streaming for large vault files
- Implement compression for vault data
- Monitor storage usage and cleanup old data

## Backup and Recovery

### Local Backup
```typescript
interface LocalBackup {
  timestamp: number;
  vaultHash: string;
  encryptedVault: string;
  metadata: BackupMetadata;
}
```

### Export/Import
- Encrypted vault export for user backup
- Cross-platform vault import capability
- Backup verification and integrity checking
- Secure backup file handling and cleanup

## Error Handling

### Storage Errors
- Graceful handling of disk full conditions
- Recovery from corrupted vault files
- Retry mechanisms with exponential backoff
- User notification for critical storage errors

### Platform-Specific Issues
- iOS: Handle keychain access errors and device passcode changes
- Android: Manage keystore availability and hardware changes
- Web: Handle storage quota exceeded and private browsing mode

### Monitoring and Diagnostics
- Log storage operations for debugging
- Monitor storage performance metrics
- Alert on repeated storage failures
- Provide diagnostic information for support