{"name": "twofai", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "build:ios": "expo build:ios", "build:android": "expo build:android", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@supabase/supabase-js": "^2.50.2", "expo": "~53.0.13", "expo-blur": "~14.1.5", "expo-camera": "~16.1.9", "expo-clipboard": "~7.1.4", "expo-crypto": "~14.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-local-authentication": "~16.0.4", "expo-modules-core": "2.4.0", "expo-router": "~5.1.1", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.475.0", "metro-react-native-babel-transformer": "^0.77.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "web-streams-polyfill": "^3.2.0"}, "devDependencies": {"@babel/core": "^7.27.7", "@types/jest": "^29.5.14", "@types/react": "~19.0.14", "cross-env": "^7.0.3", "jest": "^29.7.0", "jest-expo": "~53.0.7", "typescript": "~5.8.3"}, "jest": {"preset": "jest-expo", "collectCoverage": true, "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html", "json"], "collectCoverageFrom": ["src/**/*.{ts,tsx}", "app/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/**/*.test.{ts,tsx}", "!app/**/*.test.{ts,tsx}"], "coverageThreshold": {"global": {"branches": 85, "functions": 85, "lines": 85, "statements": 85}}, "testMatch": ["**/__tests__/**/*.(ts|tsx|js)", "**/*.(test|spec).(ts|tsx|js)"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}}}