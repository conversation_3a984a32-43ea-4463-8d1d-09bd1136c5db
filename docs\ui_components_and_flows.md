# UI Components and User Flows

## Overview

TWOFAI's user interface is built around security-first principles with intuitive navigation patterns. The component architecture emphasizes reusability, accessibility, and consistent user experience across all platforms.

## Screen Architecture

### Navigation Structure
```
App Root
├── (tabs)/
│   ├── index.tsx              # Vault (Home)
│   ├── generator.tsx          # Password Generator
│   ├── settings.tsx           # Settings
│   └── _layout.tsx           # Tab Navigation
├── auth/
│   ├── setup.tsx             # Initial Setup
│   ├── unlock.tsx            # Unlock Screen
│   └── biometric-setup.tsx   # Biometric Configuration
├── vault/
│   ├── entry/[id].tsx        # Entry Details
│   ├── edit/[id].tsx         # Edit Entry
│   ├── add.tsx              # Add New Entry
│   └── search.tsx           # Search Interface
└── modals/
    ├── sync-conflict.tsx     # Conflict Resolution
    ├── export-vault.tsx     # Export Options
    └── import-vault.tsx     # Import Options
```

## Core Components

### 1. Security Components

#### UnlockScreen
```typescript
interface UnlockScreenProps {
  onUnlock: (vaultKey: CryptoKey) => void;
  biometricEnabled: boolean;
  onBiometricSetup: () => void;
}

const UnlockScreen: React.FC<UnlockScreenProps> = ({
  onUnlock,
  biometricEnabled,
  onBiometricSetup
}) => {
  // Master password input
  // Biometric authentication trigger
  // Error handling and retry logic
  // Security indicators
};
```

#### BiometricPrompt
```typescript
interface BiometricPromptProps {
  visible: boolean;
  onSuccess: (key: string) => void;
  onError: (error: BiometricError) => void;
  onCancel: () => void;
  promptMessage?: string;
}

const BiometricPrompt: React.FC<BiometricPromptProps> = ({
  visible,
  onSuccess,
  onError,
  onCancel,
  promptMessage = "Authenticate to unlock TWOFAI"
}) => {
  // Platform-specific biometric authentication
  // Error handling and fallback options
  // Visual feedback during authentication
};
```

#### SecurityIndicator
```typescript
interface SecurityIndicatorProps {
  isLocked: boolean;
  syncStatus: 'synced' | 'syncing' | 'offline' | 'conflict';
  lastSync?: Date;
}

const SecurityIndicator: React.FC<SecurityIndicatorProps> = ({
  isLocked,
  syncStatus,
  lastSync
}) => {
  // Lock status display
  // Sync status with visual indicators
  // Last sync timestamp
  // Quick action buttons
};
```

### 2. Vault Components

#### VaultEntry
```typescript
interface VaultEntryProps {
  entry: VaultEntryData;
  onPress: () => void;
  onLongPress: () => void;
  showSensitiveData: boolean;
}

const VaultEntry: React.FC<VaultEntryProps> = ({
  entry,
  onPress,
  onLongPress,
  showSensitiveData
}) => {
  // Entry type icon (password, TOTP, note)
  // Title and subtitle display
  // Masked/unmasked sensitive data
  // Quick action buttons (copy, view)
  // TOTP countdown timer
};

// Entry type variants
const PasswordEntry: React.FC<PasswordEntryProps> = ({ entry }) => {
  // Username display
  // Masked password with reveal option
  // Copy password button
  // Strength indicator
};

const TOTPEntry: React.FC<TOTPEntryProps> = ({ entry }) => {
  // Service name and account
  // Current TOTP code
  // Countdown timer with visual indicator
  // Copy code button
};
```

#### VaultList
```typescript
interface VaultListProps {
  entries: VaultEntryData[];
  searchQuery: string;
  sortBy: 'name' | 'recent' | 'type';
  onEntryPress: (entry: VaultEntryData) => void;
  onEntryLongPress: (entry: VaultEntryData) => void;
}

const VaultList: React.FC<VaultListProps> = ({
  entries,
  searchQuery,
  sortBy,
  onEntryPress,
  onEntryLongPress
}) => {
  // Virtualized list for performance
  // Search highlighting
  // Sort controls
  // Empty state handling
  // Pull-to-refresh
};
```

### 3. Input Components

#### SecureTextInput
```typescript
interface SecureTextInputProps extends TextInputProps {
  isPassword?: boolean;
  showRevealButton?: boolean;
  strengthIndicator?: boolean;
  onStrengthChange?: (strength: PasswordStrength) => void;
}

const SecureTextInput: React.FC<SecureTextInputProps> = ({
  isPassword = false,
  showRevealButton = true,
  strengthIndicator = false,
  onStrengthChange,
  ...props
}) => {
  // Secure text entry with reveal toggle
  // Password strength calculation and display
  // Copy protection (where supported)
  // Focus/blur security handling
};
```

#### TOTPInput
```typescript
interface TOTPInputProps {
  onSecretEnter: (secret: string) => void;
  onQRScan: () => void;
  initialSecret?: string;
}

const TOTPInput: React.FC<TOTPInputProps> = ({
  onSecretEnter,
  onQRScan,
  initialSecret
}) => {
  // Manual secret entry
  // QR code scanning button
  // Secret validation
  // Preview of generated codes
};
```

### 4. Navigation Components

#### TabNavigator
```typescript
const TabNavigator: React.FC = () => {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: theme.colors.primary[500],
        tabBarInactiveTintColor: theme.colors.gray[600],
        tabBarStyle: {
          backgroundColor: theme.colors.background.primary,
          borderTopColor: theme.colors.border.light,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Vault',
          tabBarIcon: ({ color, size }) => (
            <Lock color={color} size={size} />
          ),
        }}
      />
      {/* Additional tabs */}
    </Tabs>
  );
};
```

#### Header
```typescript
interface HeaderProps {
  title: string;
  showBack?: boolean;
  rightAction?: {
    icon: string;
    onPress: () => void;
    label: string;
  };
  searchable?: boolean;
  onSearch?: (query: string) => void;
}

const Header: React.FC<HeaderProps> = ({
  title,
  showBack = false,
  rightAction,
  searchable = false,
  onSearch
}) => {
  // Title display
  // Back navigation
  // Search functionality
  // Action buttons
  // Status indicators
};
```

## User Flow Diagrams

### 1. Initial Setup Flow
```mermaid
graph TD
    A[App Launch] --> B{First Time?}
    B -->|Yes| C[Welcome Screen]
    B -->|No| D[Unlock Screen]
    C --> E[Create Master Password]
    E --> F[Confirm Password]
    F --> G{Enable Biometrics?}
    G -->|Yes| H[Setup Biometrics]
    G -->|No| I[Create Empty Vault]
    H --> J[Test Biometric Auth]
    J --> K{Success?}
    K -->|Yes| I
    K -->|No| L[Biometric Setup Failed]
    L --> I
    I --> M[Setup Complete]
```

### 2. Daily Usage Flow
```mermaid
graph TD
    A[App Launch] --> B[Unlock Screen]
    B --> C{Biometric Available?}
    C -->|Yes| D[Biometric Auth]
    C -->|No| E[Master Password]
    D --> F{Auth Success?}
    F -->|Yes| G[Vault Screen]
    F -->|No| H{Retry?}
    H -->|Yes| D
    H -->|No| E
    E --> I[Enter Password]
    I --> J{Valid Password?}
    J -->|Yes| G
    J -->|No| K[Show Error]
    K --> I
    G --> L[Browse/Search Entries]
    L --> M[Select Entry]
    M --> N[View/Copy/Edit]
```

### 3. Add Entry Flow
```mermaid
graph TD
    A[Add Button] --> B[Select Entry Type]
    B --> C{Type?}
    C -->|Password| D[Password Form]
    C -->|TOTP| E[TOTP Setup]
    C -->|Note| F[Note Form]
    D --> G[Enter Details]
    G --> H[Generate Password?]
    H -->|Yes| I[Password Generator]
    H -->|No| J[Save Entry]
    I --> J
    E --> K{Setup Method?}
    K -->|QR Code| L[Scan QR]
    K -->|Manual| M[Enter Secret]
    L --> N[Validate & Preview]
    M --> N
    N --> J
    F --> O[Enter Note Content]
    O --> J
    J --> P[Encrypt & Store]
    P --> Q[Return to Vault]
```

## Component Patterns

### 1. Container Components
Handle state management and business logic:

```typescript
// VaultContainer handles vault operations
const VaultContainer: React.FC = () => {
  const { vault, isLoading, error } = useVault();
  const { searchQuery, filteredEntries } = useVaultSearch(vault);
  
  return (
    <VaultPresentation
      entries={filteredEntries}
      isLoading={isLoading}
      error={error}
      onAddEntry={handleAddEntry}
      onEntryPress={handleEntryPress}
    />
  );
};
```

### 2. Presentation Components
Handle display and user interaction:

```typescript
// VaultPresentation focuses on UI
const VaultPresentation: React.FC<VaultPresentationProps> = ({
  entries,
  isLoading,
  error,
  onAddEntry,
  onEntryPress
}) => {
  if (isLoading) return <LoadingState />;
  if (error) return <ErrorState error={error} />;
  if (entries.length === 0) return <EmptyState onAddEntry={onAddEntry} />;
  
  return (
    <VaultList entries={entries} onEntryPress={onEntryPress} />
  );
};
```

### 3. Compound Components
Complex components with multiple related parts:

```typescript
// PasswordGenerator compound component
const PasswordGenerator = {
  Root: PasswordGeneratorRoot,
  Options: PasswordGeneratorOptions,
  Output: PasswordGeneratorOutput,
  Actions: PasswordGeneratorActions,
};

// Usage
<PasswordGenerator.Root>
  <PasswordGenerator.Options />
  <PasswordGenerator.Output />
  <PasswordGenerator.Actions />
</PasswordGenerator.Root>
```

## State Management Patterns

### 1. Local State with Hooks
```typescript
const useVaultEntry = (entryId: string) => {
  const [entry, setEntry] = useState<VaultEntry | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const updateEntry = useCallback(async (updates: Partial<VaultEntry>) => {
    try {
      const updatedEntry = await vaultService.updateEntry(entryId, updates);
      setEntry(updatedEntry);
    } catch (err) {
      setError(err.message);
    }
  }, [entryId]);
  
  return { entry, isLoading, error, updateEntry };
};
```

### 2. Context for Global State
```typescript
const VaultContext = createContext<VaultContextValue | null>(null);

const VaultProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [vault, setVault] = useState<DecryptedVault | null>(null);
  const [isLocked, setIsLocked] = useState(true);
  
  const value = useMemo(() => ({
    vault,
    isLocked,
    unlock: handleUnlock,
    lock: handleLock,
    addEntry: handleAddEntry,
    updateEntry: handleUpdateEntry,
    deleteEntry: handleDeleteEntry,
  }), [vault, isLocked]);
  
  return (
    <VaultContext.Provider value={value}>
      {children}
    </VaultContext.Provider>
  );
};
```

## Error Handling Patterns

### 1. Error Boundaries
```typescript
class VaultErrorBoundary extends React.Component<
  { children: ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error for debugging
    console.error('Vault error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}
```

### 2. Error States
```typescript
const ErrorState: React.FC<{ error: Error; onRetry?: () => void }> = ({
  error,
  onRetry
}) => {
  const errorMessage = getUserFriendlyErrorMessage(error);
  
  return (
    <View style={styles.errorContainer}>
      <AlertCircle size={48} color={theme.colors.error.light} />
      <Text style={styles.errorTitle}>Something went wrong</Text>
      <Text style={styles.errorMessage}>{errorMessage}</Text>
      {onRetry && (
        <Button onPress={onRetry} variant="secondary">
          Try Again
        </Button>
      )}
    </View>
  );
};
```

## Loading States

### 1. Skeleton Loading
```typescript
const VaultSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      {Array.from({ length: 5 }).map((_, index) => (
        <View key={index} style={styles.skeletonItem}>
          <SkeletonPlaceholder>
            <View style={styles.skeletonIcon} />
            <View style={styles.skeletonContent}>
              <View style={styles.skeletonTitle} />
              <View style={styles.skeletonSubtitle} />
            </View>
          </SkeletonPlaceholder>
        </View>
      ))}
    </View>
  );
};
```

### 2. Progressive Loading
```typescript
const VaultScreen: React.FC = () => {
  const [loadingStage, setLoadingStage] = useState<'decrypting' | 'loading' | 'ready'>('decrypting');
  
  useEffect(() => {
    const loadVault = async () => {
      setLoadingStage('decrypting');
      await decryptVault();
      
      setLoadingStage('loading');
      await loadVaultEntries();
      
      setLoadingStage('ready');
    };
    
    loadVault();
  }, []);
  
  if (loadingStage === 'decrypting') {
    return <DecryptionProgress />;
  }
  
  if (loadingStage === 'loading') {
    return <VaultSkeleton />;
  }
  
  return <VaultList />;
};
```

## Accessibility Implementation

### 1. Screen Reader Support
```typescript
const VaultEntry: React.FC<VaultEntryProps> = ({ entry }) => {
  const accessibilityLabel = `${entry.title}, ${entry.type} entry, ${
    entry.type === 'password' ? 'username ' + entry.username : ''
  }`;
  
  return (
    <TouchableOpacity
      accessible={true}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole="button"
      accessibilityHint="Double tap to view entry details"
    >
      {/* Entry content */}
    </TouchableOpacity>
  );
};
```

### 2. Focus Management
```typescript
const Modal: React.FC<ModalProps> = ({ visible, onClose, children }) => {
  const firstFocusableRef = useRef<View>(null);
  
  useEffect(() => {
    if (visible && firstFocusableRef.current) {
      // Focus first element when modal opens
      firstFocusableRef.current.focus();
    }
  }, [visible]);
  
  const handleBackdropPress = () => {
    onClose();
    // Return focus to trigger element
  };
  
  return (
    <ReactNativeModal
      visible={visible}
      onBackdropPress={handleBackdropPress}
      accessibilityViewIsModal={true}
    >
      <View ref={firstFocusableRef}>
        {children}
      </View>
    </ReactNativeModal>
  );
};
```

## Performance Optimization

### 1. List Virtualization
```typescript
const VirtualizedVaultList: React.FC<VaultListProps> = ({ entries }) => {
  const renderItem = useCallback(({ item }: { item: VaultEntry }) => (
    <VaultEntryItem entry={item} />
  ), []);
  
  const keyExtractor = useCallback((item: VaultEntry) => item.id, []);
  
  return (
    <FlatList
      data={entries}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      updateCellsBatchingPeriod={50}
      initialNumToRender={10}
      windowSize={10}
      getItemLayout={(data, index) => ({
        length: ITEM_HEIGHT,
        offset: ITEM_HEIGHT * index,
        index,
      })}
    />
  );
};
```

### 2. Memoization
```typescript
const VaultEntry = React.memo<VaultEntryProps>(({ entry, onPress }) => {
  const handlePress = useCallback(() => {
    onPress(entry);
  }, [entry, onPress]);
  
  return (
    <TouchableOpacity onPress={handlePress}>
      {/* Entry content */}
    </TouchableOpacity>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for memo
  return prevProps.entry.id === nextProps.entry.id &&
         prevProps.entry.updatedAt === nextProps.entry.updatedAt;
});
```

## Testing Patterns

### 1. Component Testing
```typescript
describe('VaultEntry', () => {
  it('should display entry title and type', () => {
    const entry = createMockVaultEntry();
    render(<VaultEntry entry={entry} onPress={jest.fn()} />);
    
    expect(screen.getByText(entry.title)).toBeVisible();
    expect(screen.getByText(entry.type)).toBeVisible();
  });
  
  it('should call onPress when tapped', () => {
    const entry = createMockVaultEntry();
    const onPress = jest.fn();
    render(<VaultEntry entry={entry} onPress={onPress} />);
    
    fireEvent.press(screen.getByRole('button'));
    expect(onPress).toHaveBeenCalledWith(entry);
  });
});
```

### 2. Integration Testing
```typescript
describe('Vault Flow', () => {
  it('should allow adding a new password entry', async () => {
    const { getByText, getByPlaceholderText } = render(<VaultScreen />);
    
    // Navigate to add entry
    fireEvent.press(getByText('Add Entry'));
    fireEvent.press(getByText('Password'));
    
    // Fill form
    fireEvent.changeText(getByPlaceholderText('Title'), 'Test Site');
    fireEvent.changeText(getByPlaceholderText('Username'), 'testuser');
    fireEvent.changeText(getByPlaceholderText('Password'), 'testpass');
    
    // Save entry
    fireEvent.press(getByText('Save'));
    
    // Verify entry appears in list
    await waitFor(() => {
      expect(getByText('Test Site')).toBeVisible();
    });
  });
});
```