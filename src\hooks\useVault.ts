import { useState, useCallback, useEffect } from 'react';
import { VaultManager } from '../services/vault/vaultManager';
import { DecryptedVault, AnyVaultEntry, NewVaultEntry } from '../types/vault';

interface UseVaultReturn {
  vault: DecryptedVault | null;
  isLocked: boolean;
  isLoading: boolean;
  error: string | null;
  createVault: (masterPassword: string) => Promise<void>;
  unlockVault: (masterPassword: string) => Promise<void>;
  lockVault: () => void;
  addEntry: (entry: NewVaultEntry) => Promise<AnyVaultEntry>;
  updateEntry: (id: string, updates: Partial<AnyVaultEntry>) => Promise<AnyVaultEntry>;
  deleteEntry: (id: string) => Promise<void>;
  searchEntries: (query: string) => AnyVaultEntry[];
  clearError: () => void;
}

export const useVault = (): UseVaultReturn => {
  const [vault, setVault] = useState<DecryptedVault | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const vaultManager = VaultManager.getInstance();

  const createVault = useCallback(async (masterPassword: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await vaultManager.createVault(masterPassword);
      const newVault = vaultManager.getVault();
      setVault(newVault);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create vault');
    } finally {
      setIsLoading(false);
    }
  }, [vaultManager]);

  const unlockVault = useCallback(async (masterPassword: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const unlockedVault = await vaultManager.unlockVault(masterPassword);
      setVault(unlockedVault);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to unlock vault');
    } finally {
      setIsLoading(false);
    }
  }, [vaultManager]);

  const lockVault = useCallback(() => {
    vaultManager.lockVault();
    setVault(null);
    setError(null);
  }, [vaultManager]);

  const addEntry = useCallback(async (entry: NewVaultEntry): Promise<AnyVaultEntry> => {
    setError(null);
    
    try {
      const newEntry = await vaultManager.addEntry(entry);
      const updatedVault = vaultManager.getVault();
      setVault(updatedVault);
      return newEntry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add entry';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [vaultManager]);

  const updateEntry = useCallback(async (id: string, updates: Partial<AnyVaultEntry>): Promise<AnyVaultEntry> => {
    setError(null);
    
    try {
      const updatedEntry = await vaultManager.updateEntry(id, updates);
      const updatedVault = vaultManager.getVault();
      setVault(updatedVault);
      return updatedEntry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update entry';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [vaultManager]);

  const deleteEntry = useCallback(async (id: string): Promise<void> => {
    setError(null);
    
    try {
      await vaultManager.deleteEntry(id);
      const updatedVault = vaultManager.getVault();
      setVault(updatedVault);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete entry';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [vaultManager]);

  const searchEntries = useCallback((query: string): AnyVaultEntry[] => {
    return vaultManager.searchEntries(query);
  }, [vaultManager]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Check if vault is locked
  const isLocked = vault === null;

  return {
    vault,
    isLocked,
    isLoading,
    error,
    createVault,
    unlockVault,
    lockVault,
    addEntry,
    updateEntry,
    deleteEntry,
    searchEntries,
    clearError,
  };
};