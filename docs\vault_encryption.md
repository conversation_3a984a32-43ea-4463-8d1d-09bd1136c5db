# Vault Encryption Architecture

## Overview

TWOFAI implements zero-knowledge encryption using industry-standard cryptographic primitives. All vault data is encrypted client-side before storage, ensuring that neither the application servers nor any third parties can access user data.

## Key Derivation

### Master Password Processing
```typescript
// Argon2id parameters for key derivation
const ARGON2_CONFIG = {
  timeCost: 3,        // Number of iterations
  memoryCost: 65536,  // Memory usage in KB (64MB)
  parallelism: 1,     // Number of parallel threads
  hashLength: 32,     // Output length in bytes (256-bit key)
  type: 'argon2id'    // Recommended variant
};
```

### Salt Generation
- Each vault uses a unique 32-byte cryptographically secure random salt
- Salt is generated once during vault creation and stored alongside encrypted data
- Never reuse salts across different vaults or users

### Vault Key Derivation Flow
```
Master Password + Salt → Argon2id → 256-bit Vault Key
```

## Encryption Scheme

### AES-256-GCM Configuration
```typescript
const ENCRYPTION_CONFIG = {
  algorithm: 'AES-GCM',
  keyLength: 256,     // 256-bit key
  ivLength: 96,       // 12-byte IV (96 bits)
  tagLength: 128      // 16-byte authentication tag (128 bits)
};
```

### Encryption Process
1. Generate random 12-byte IV for each encryption operation
2. Encrypt plaintext using AES-256-GCM with vault key and IV
3. Append authentication tag to ciphertext
4. Store IV + ciphertext + tag as encrypted blob

### Data Structure
```typescript
interface EncryptedVault {
  version: number;           // Schema version for migration
  salt: string;             // Base64-encoded 32-byte salt
  iv: string;               // Base64-encoded 12-byte IV
  ciphertext: string;       // Base64-encoded encrypted data
  tag: string;              // Base64-encoded 16-byte auth tag
  timestamp: number;        // Creation/update timestamp
}
```

## In-Memory Vault Management

### Memory Safety Principles
- Decrypted vault data exists only in React state/context
- No persistent storage of decrypted data or encryption keys
- Automatic memory cleanup on app backgrounding/locking
- Secure string handling where possible

### Vault Lifecycle
```typescript
interface VaultState {
  isLocked: boolean;
  vaultKey: CryptoKey | null;    // WebCrypto key object
  decryptedVault: Vault | null;  // Plaintext vault data
  lastActivity: number;          // For auto-lock timing
}
```

### Key Rotation Support
- Each vault supports key rotation by re-encrypting with new derived key
- Maintains backward compatibility during transition period
- Automatic rotation triggers (time-based, security events)

## Cryptographic Implementation

### WebCrypto API Usage
```typescript
// Key import from derived bytes
const vaultKey = await crypto.subtle.importKey(
  'raw',
  derivedKeyBytes,
  { name: 'AES-GCM' },
  false, // Not extractable
  ['encrypt', 'decrypt']
);

// Encryption
const encrypted = await crypto.subtle.encrypt(
  {
    name: 'AES-GCM',
    iv: randomIV,
    tagLength: 128
  },
  vaultKey,
  new TextEncoder().encode(plaintext)
);
```

### Error Handling
- Constant-time comparison for authentication tags
- Secure cleanup on decryption failures
- Rate limiting for repeated decryption attempts
- Memory zeroing for sensitive buffers (where supported)

## Security Considerations

### Threat Model
- **Protects against**: Device theft, cloud storage breaches, network interception
- **Assumes secure**: Device keychain/secure element, biometric authentication
- **Mitigates**: Timing attacks, memory dumps, side-channel attacks

### Key Security Properties
- **Confidentiality**: Only user with master password can decrypt vault
- **Integrity**: Authentication tags prevent tampering
- **Authenticity**: GCM mode provides authenticated encryption
- **Forward secrecy**: Key rotation provides forward secrecy

### Performance Considerations
- Argon2id parameters tuned for ~500ms on modern mobile devices
- Batch encryption/decryption for multiple vault entries
- Progressive decryption for large vaults
- Memory usage monitoring and cleanup

## Compliance and Standards

### Cryptographic Standards
- **NIST SP 800-38D**: AES-GCM mode specification
- **RFC 9106**: Argon2 password hashing standard
- **FIPS 140-2**: Approved cryptographic modules (where available)

### Implementation Validation
- Test vectors for Argon2id implementation
- Known-answer tests for AES-GCM
- Cross-platform compatibility testing
- Security audit checkpoints