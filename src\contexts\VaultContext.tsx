import React, { createContext, useContext, ReactNode } from 'react';
import { useVault } from '../hooks/useVault';
import { DecryptedVault, AnyVaultEntry, NewVaultEntry } from '../types/vault';

interface VaultContextValue {
  vault: DecryptedVault | null;
  isLocked: boolean;
  isLoading: boolean;
  error: string | null;
  createVault: (masterPassword: string) => Promise<void>;
  unlockVault: (masterPassword: string) => Promise<void>;
  lockVault: () => void;
  addEntry: (entry: NewVaultEntry) => Promise<AnyVaultEntry>;
  updateEntry: (id: string, updates: Partial<AnyVaultEntry>) => Promise<AnyVaultEntry>;
  deleteEntry: (id: string) => Promise<void>;
  searchEntries: (query: string) => AnyVaultEntry[];
  clearError: () => void;
}

const VaultContext = createContext<VaultContextValue | null>(null);

interface VaultProviderProps {
  children: ReactNode;
}

export const VaultProvider: React.FC<VaultProviderProps> = ({ children }) => {
  const vaultState = useVault();

  return (
    <VaultContext.Provider value={vaultState}>
      {children}
    </VaultContext.Provider>
  );
};

export const useVaultContext = (): VaultContextValue => {
  const context = useContext(VaultContext);
  if (!context) {
    throw new Error('useVaultContext must be used within a VaultProvider');
  }
  return context;
};