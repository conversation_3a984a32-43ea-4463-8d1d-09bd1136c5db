import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { CardProps } from '../../types/ui';
import { lightTheme } from '../../config/theme';

export const Card: React.FC<CardProps> = ({
  children,
  onPress,
  padding = 'lg',
  margin,
  shadow = 'md',
  variant = 'default',
}) => {
  const theme = lightTheme; // TODO: Use theme context

  const getCardStyle = () => {
    const baseStyle = [styles.card];
    
    // Padding
    switch (padding) {
      case 'xs':
        baseStyle.push({ padding: theme.spacing.xs });
        break;
      case 'sm':
        baseStyle.push({ padding: theme.spacing.sm });
        break;
      case 'md':
        baseStyle.push({ padding: theme.spacing.md });
        break;
      case 'lg':
        baseStyle.push({ padding: theme.spacing.lg });
        break;
      case 'xl':
        baseStyle.push({ padding: theme.spacing.xl });
        break;
    }

    // Margin
    if (margin) {
      switch (margin) {
        case 'xs':
          baseStyle.push({ margin: theme.spacing.xs });
          break;
        case 'sm':
          baseStyle.push({ margin: theme.spacing.sm });
          break;
        case 'md':
          baseStyle.push({ margin: theme.spacing.md });
          break;
        case 'lg':
          baseStyle.push({ margin: theme.spacing.lg });
          break;
        case 'xl':
          baseStyle.push({ margin: theme.spacing.xl });
          break;
      }
    }

    // Shadow
    switch (shadow) {
      case 'sm':
        baseStyle.push(theme.shadows.sm);
        break;
      case 'md':
        baseStyle.push(theme.shadows.md);
        break;
      case 'lg':
        baseStyle.push(theme.shadows.lg);
        break;
      case 'xl':
        baseStyle.push(theme.shadows.xl);
        break;
    }

    // Variant
    switch (variant) {
      case 'outlined':
        baseStyle.push(styles.cardOutlined);
        break;
      case 'elevated':
        baseStyle.push(styles.cardElevated);
        break;
      default:
        baseStyle.push(styles.cardDefault);
    }

    return baseStyle;
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={getCardStyle()}
        onPress={onPress}
        activeOpacity={0.7}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={getCardStyle()}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    backgroundColor: '#ffffff',
  },
  cardDefault: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  cardOutlined: {
    borderWidth: 2,
    borderColor: '#d1d5db',
    backgroundColor: 'transparent',
  },
  cardElevated: {
    borderWidth: 0,
  },
});