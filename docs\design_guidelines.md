# Design System Guidelines

## Overview

TWOFAI implements a cohesive design system focused on security, accessibility, and cross-platform consistency. The design prioritizes clarity, trust, and ease of use while maintaining a modern, professional appearance.

## Design Principles

### Security-First Design
- **Clear visual hierarchy** for security-critical actions
- **High contrast** for important security indicators
- **Consistent iconography** for security states (locked/unlocked)
- **Visual feedback** for security operations (encryption, sync)

### Accessibility Excellence
- **WCAG 2.1 AA compliance** minimum standard
- **4.5:1 contrast ratio** for normal text
- **3:1 contrast ratio** for large text and UI elements
- **Touch target size** minimum 44px × 44px
- **Screen reader optimization** with semantic markup

### Cross-Platform Harmony
- **Consistent visual language** across iOS, Android, and Web
- **Platform-adaptive components** that respect OS conventions
- **Responsive design** for various screen sizes
- **Performance-optimized** animations and transitions

## Color System

### Primary Palette
```typescript
const colors = {
  primary: {
    50: '#eff6ff',   // Very light blue
    100: '#dbeafe',  // Light blue
    200: '#bfdbfe',  // Lighter blue
    300: '#93c5fd',  // Light blue
    400: '#60a5fa',  // Medium blue
    500: '#3b82f6',  // Primary blue
    600: '#2563eb',  // Darker blue
    700: '#1d4ed8',  // Dark blue
    800: '#1e40af',  // Very dark blue
    900: '#1e3a8a',  // Darkest blue
  }
};
```

### Semantic Colors
```typescript
const semanticColors = {
  success: {
    light: '#10b981',  // Green
    dark: '#059669',   // Dark green
  },
  warning: {
    light: '#f59e0b',  // Amber
    dark: '#d97706',   // Dark amber
  },
  error: {
    light: '#ef4444',  // Red
    dark: '#dc2626',   // Dark red
  },
  info: {
    light: '#06b6d4',  // Cyan
    dark: '#0891b2',   // Dark cyan
  }
};
```

### Neutral Palette
```typescript
const neutrals = {
  white: '#ffffff',
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  black: '#000000',
};
```

### Dark Mode Adaptations
```typescript
const darkModeColors = {
  background: {
    primary: '#0f172a',    // Slate 900
    secondary: '#1e293b',  // Slate 800
    tertiary: '#334155',   // Slate 700
  },
  text: {
    primary: '#f1f5f9',    // Slate 100
    secondary: '#cbd5e1',  // Slate 300
    tertiary: '#94a3b8',   // Slate 400
  },
  border: {
    light: '#334155',      // Slate 700
    medium: '#475569',     // Slate 600
    strong: '#64748b',     // Slate 500
  }
};
```

## Typography System

### Font Families
```typescript
const fonts = {
  primary: 'Inter',      // Main UI font
  monospace: 'SF Mono',  // Codes, passwords, technical data
};

// Font loading configuration
const fontWeights = {
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
};
```

### Type Scale
```typescript
const typography = {
  // Headings
  h1: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: 'bold',
    letterSpacing: -0.5,
  },
  h2: {
    fontSize: 24,
    lineHeight: 29,
    fontWeight: 'bold',
    letterSpacing: -0.25,
  },
  h3: {
    fontSize: 20,
    lineHeight: 24,
    fontWeight: 'semibold',
    letterSpacing: 0,
  },
  h4: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: 'semibold',
    letterSpacing: 0,
  },
  
  // Body text
  body: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: 'regular',
    letterSpacing: 0,
  },
  bodySmall: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: 'regular',
    letterSpacing: 0,
  },
  
  // UI elements
  button: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: 'medium',
    letterSpacing: 0,
  },
  caption: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: 'regular',
    letterSpacing: 0.5,
  },
  
  // Technical text
  code: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: 'regular',
    fontFamily: 'monospace',
    letterSpacing: 0,
  },
};
```

## Spacing System

### Base Unit: 4px
All spacing values are multiples of 4px for consistent rhythm and alignment.

```typescript
const spacing = {
  xs: 4,    // 0.25rem
  sm: 8,    // 0.5rem
  md: 12,   // 0.75rem
  lg: 16,   // 1rem
  xl: 20,   // 1.25rem
  '2xl': 24, // 1.5rem
  '3xl': 32, // 2rem
  '4xl': 40, // 2.5rem
  '5xl': 48, // 3rem
  '6xl': 64, // 4rem
};
```

### Component Spacing
```typescript
const componentSpacing = {
  // Card padding
  cardPadding: 16,
  cardGap: 12,
  
  // Button padding
  buttonPaddingY: 12,
  buttonPaddingX: 16,
  
  // Input padding
  inputPaddingY: 12,
  inputPaddingX: 16,
  
  // List item padding
  listItemPaddingY: 16,
  listItemPaddingX: 16,
  
  // Section margins
  sectionMarginBottom: 24,
  sectionMarginTop: 32,
};
```

## Border Radius System

```typescript
const borderRadius = {
  none: 0,
  sm: 4,    // Small elements
  md: 8,    // Default cards, buttons
  lg: 12,   // Large cards
  xl: 16,   // Hero cards
  '2xl': 24, // Modal cards
  full: 9999, // Pills, circular buttons
};
```

## Shadow System

### Light Mode Shadows
```typescript
const shadows = {
  sm: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  lg: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 6,
  },
  xl: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.25,
    shadowRadius: 25,
    elevation: 10,
  },
};
```

## Component Guidelines

### Buttons

#### Primary Button
```typescript
const primaryButton = {
  backgroundColor: colors.primary[500],
  paddingVertical: 12,
  paddingHorizontal: 24,
  borderRadius: borderRadius.md,
  minHeight: 44, // Accessibility minimum
};

// States
const primaryButtonStates = {
  hover: { backgroundColor: colors.primary[600] },
  pressed: { backgroundColor: colors.primary[700] },
  disabled: { 
    backgroundColor: colors.gray[300],
    opacity: 0.6,
  },
};
```

#### Secondary Button
```typescript
const secondaryButton = {
  backgroundColor: 'transparent',
  borderWidth: 1,
  borderColor: colors.primary[500],
  paddingVertical: 12,
  paddingHorizontal: 24,
  borderRadius: borderRadius.md,
};
```

### Input Fields
```typescript
const inputField = {
  borderWidth: 1,
  borderColor: colors.gray[300],
  borderRadius: borderRadius.md,
  paddingVertical: 12,
  paddingHorizontal: 16,
  fontSize: 16,
  minHeight: 44,
  
  // Focus state
  focusStyle: {
    borderColor: colors.primary[500],
    borderWidth: 2,
  },
  
  // Error state
  errorStyle: {
    borderColor: semanticColors.error.light,
    borderWidth: 2,
  },
};
```

### Cards
```typescript
const card = {
  backgroundColor: colors.white,
  borderRadius: borderRadius.lg,
  padding: 16,
  ...shadows.md,
  
  // Dark mode
  darkMode: {
    backgroundColor: darkModeColors.background.secondary,
    borderWidth: 1,
    borderColor: darkModeColors.border.light,
  },
};
```

### Navigation
```typescript
const tabBar = {
  backgroundColor: colors.white,
  borderTopWidth: 1,
  borderTopColor: colors.gray[200],
  paddingBottom: 8, // Extra padding for safe area
  height: 82,
  
  // Tab item
  tabItem: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    minWidth: 44,
    minHeight: 44,
  },
};
```

## Icon System

### Icon Specifications
```typescript
const iconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 48,
};

const iconColors = {
  primary: colors.primary[500],
  secondary: colors.gray[600],
  success: semanticColors.success.light,
  warning: semanticColors.warning.light,
  error: semanticColors.error.light,
};
```

### Security Icons
```typescript
const securityIcons = {
  locked: 'lock',
  unlocked: 'unlock',
  biometric: 'fingerprint',
  password: 'key',
  twoFactor: 'shield-check',
  sync: 'refresh-cw',
  offline: 'wifi-off',
  secure: 'shield',
};
```

## Animation Guidelines

### Timing Functions
```typescript
const easings = {
  linear: 'linear',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
  spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
};
```

### Duration Scale
```typescript
const durations = {
  instant: 0,
  fast: 150,
  normal: 250,
  slow: 350,
  slower: 500,
};
```

### Common Animations
```typescript
const animations = {
  // Fade in/out
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
    duration: durations.fast,
    easing: easings.easeOut,
  },
  
  // Scale animation for buttons
  buttonPress: {
    from: { transform: [{ scale: 1 }] },
    to: { transform: [{ scale: 0.95 }] },
    duration: durations.fast,
    easing: easings.easeInOut,
  },
  
  // Slide animations
  slideInFromRight: {
    from: { transform: [{ translateX: 300 }] },
    to: { transform: [{ translateX: 0 }] },
    duration: durations.normal,
    easing: easings.easeOut,
  },
};
```

## Layout Guidelines

### Grid System
```typescript
const grid = {
  containerPadding: 16,
  gutterWidth: 16,
  maxWidth: 1200,
  
  // Breakpoints
  breakpoints: {
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
  },
};
```

### Safe Areas
```typescript
const safeAreas = {
  top: 44,      // Status bar height
  bottom: 34,   // Home indicator height
  horizontal: 16, // Side margins
};
```

## Accessibility Guidelines

### Touch Targets
- Minimum size: 44px × 44px
- Recommended size: 48px × 48px for primary actions
- Adequate spacing between touch targets (8px minimum)

### Color Contrast
- Normal text: 4.5:1 minimum ratio
- Large text (18px+): 3:1 minimum ratio
- UI elements: 3:1 minimum ratio
- Focus indicators: 3:1 minimum ratio

### Screen Reader Support
```typescript
const accessibilityLabels = {
  // Common labels
  close: 'Close',
  back: 'Go back',
  menu: 'Open menu',
  search: 'Search',
  
  // Security-specific
  unlock: 'Unlock vault',
  lock: 'Lock vault',
  biometricUnlock: 'Unlock with biometrics',
  showPassword: 'Show password',
  hidePassword: 'Hide password',
  copyPassword: 'Copy password to clipboard',
  
  // Navigation
  homeTab: 'Home tab',
  settingsTab: 'Settings tab',
  vaultTab: 'Vault tab',
};
```

### Focus Management
- Logical focus order
- Visible focus indicators
- Focus trapping in modals
- Return focus after dismissing overlays

## Theme Implementation

### Theme Provider
```typescript
interface Theme {
  colors: typeof colors;
  typography: typeof typography;
  spacing: typeof spacing;
  borderRadius: typeof borderRadius;
  shadows: typeof shadows;
  isDark: boolean;
}

const lightTheme: Theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  isDark: false,
};

const darkTheme: Theme = {
  ...lightTheme,
  colors: { ...colors, ...darkModeColors },
  isDark: true,
};
```

### Dynamic Theme Switching
```typescript
const useTheme = () => {
  const [isDark, setIsDark] = useState(false);
  
  useEffect(() => {
    // Listen to system theme changes
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setIsDark(colorScheme === 'dark');
    });
    
    return () => subscription?.remove();
  }, []);
  
  return isDark ? darkTheme : lightTheme;
};
```

## Component Library Structure

### Base Components
- Button (Primary, Secondary, Text, Icon)
- Input (Text, Password, Search)
- Card (Basic, Interactive, Loading)
- Modal (Alert, Confirmation, Custom)
- Loading (Spinner, Skeleton, Progress)

### Composed Components
- VaultEntry (Password, TOTP, Note)
- SecurityIndicator (Lock status, Sync status)
- BiometricPrompt (Face ID, Touch ID, Fingerprint)
- ConflictResolution (Manual merge interface)
- PasswordGenerator (Settings and output)

### Layout Components
- Screen (Safe area wrapper)
- Section (Content grouping)
- List (Virtualized lists)
- Tabs (Bottom navigation)
- Header (Navigation header)

## Quality Assurance

### Design Review Checklist
- [ ] Consistent spacing using 4px base unit
- [ ] Proper color contrast ratios
- [ ] Touch targets meet accessibility requirements
- [ ] Consistent use of typography scale
- [ ] Proper focus states and indicators
- [ ] Dark mode compatibility
- [ ] Cross-platform consistency
- [ ] Animation performance
- [ ] Loading states implemented
- [ ] Error states handled
- [ ] Empty states designed
- [ ] Responsive design tested

### Testing Guidelines
- Visual regression testing across themes
- Accessibility testing with screen readers
- Touch target testing on various devices
- Performance testing of animations
- Cross-platform visual consistency testing