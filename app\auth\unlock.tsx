import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Lock, Fingerprint, Eye, EyeOff } from 'lucide-react-native';
import { router } from 'expo-router';
import { Platform } from 'react-native';
import * as LocalAuthentication from 'expo-local-authentication';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';

export default function UnlockScreen() {
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [failedAttempts, setFailedAttempts] = useState(0);

  useEffect(() => {
    checkBiometricAvailability();
  }, []);

  const checkBiometricAvailability = async () => {
    if (Platform.OS === 'web') {
      setBiometricAvailable(false);
      return;
    }

    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      setBiometricAvailable(hasHardware && isEnrolled);
    } catch (error) {
      console.log('Biometric check failed:', error);
      setBiometricAvailable(false);
    }
  };

  const handleBiometricAuth = async () => {
    if (Platform.OS === 'web') {
      Alert.alert('Not Available', 'Biometric authentication is not available on web.');
      return;
    }

    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Unlock TWOFAI',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Password',
      });

      if (result.success) {
        router.replace('/(tabs)');
      } else {
        console.log('Biometric authentication failed');
      }
    } catch (error) {
      console.log('Biometric authentication error:', error);
    }
  };

  const handlePasswordAuth = async () => {
    if (!password.trim()) {
      Alert.alert('Error', 'Please enter your master password');
      return;
    }

    setIsLoading(true);

    // Simulate password verification
    setTimeout(() => {
      setIsLoading(false);
      
      // Mock password check - in real app, this would verify against stored hash
      if (password === 'demo123') {
        router.replace('/(tabs)');
      } else {
        setFailedAttempts(prev => prev + 1);
        setPassword('');
        Alert.alert('Error', 'Invalid master password');
        
        if (failedAttempts >= 4) {
          Alert.alert('Account Locked', 'Too many failed attempts. Please try again later.');
        }
      }
    }, 1000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Lock size={48} color="#3b82f6" />
          </View>
          <Text style={styles.title}>Welcome Back</Text>
          <Text style={styles.subtitle}>Enter your master password to unlock your vault</Text>
        </View>

        {/* Biometric Authentication */}
        {biometricAvailable && (
          <View style={styles.biometricSection}>
            <TouchableOpacity style={styles.biometricButton} onPress={handleBiometricAuth}>
              <Fingerprint size={32} color="#3b82f6" />
              <Text style={styles.biometricText}>Use Biometric Authentication</Text>
            </TouchableOpacity>
            
            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>or</Text>
              <View style={styles.dividerLine} />
            </View>
          </View>
        )}

        {/* Password Input */}
        <View style={styles.passwordSection}>
          <Input
            label="Master Password"
            placeholder="Enter your master password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!showPassword}
            rightIcon={showPassword ? <EyeOff size={20} color="#6b7280" /> : <Eye size={20} color="#6b7280" />}
            onRightIconPress={() => setShowPassword(!showPassword)}
            autoCapitalize="none"
            autoCorrect={false}
          />

          <Button
            onPress={handlePasswordAuth}
            loading={isLoading}
            disabled={!password.trim() || isLoading}
            fullWidth
          >
            Unlock Vault
          </Button>
        </View>

        {/* Failed Attempts Warning */}
        {failedAttempts > 0 && (
          <View style={styles.warningContainer}>
            <Text style={styles.warningText}>
              {failedAttempts} failed attempt{failedAttempts > 1 ? 's' : ''}
              {failedAttempts >= 3 && ' - Account will be locked after 5 attempts'}
            </Text>
          </View>
        )}

        {/* Demo Info */}
        <View style={styles.demoInfo}>
          <Text style={styles.demoText}>Demo Password: demo123</Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  iconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    backgroundColor: '#eff6ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#dbeafe',
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
  biometricSection: {
    marginBottom: 32,
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  biometricText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#3b82f6',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#e2e8f0',
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
  passwordSection: {
    gap: 20,
  },
  warningContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  warningText: {
    fontSize: 14,
    color: '#dc2626',
    textAlign: 'center',
  },
  demoInfo: {
    marginTop: 32,
    padding: 16,
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#bae6fd',
  },
  demoText: {
    fontSize: 14,
    color: '#0369a1',
    textAlign: 'center',
    fontWeight: '500',
  },
});