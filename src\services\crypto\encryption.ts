import * as Crypto from 'expo-crypto';
import { CRYPTO_CONFIG } from '../../config/security';
import { EncryptionResult, DecryptionInput, CryptoError } from '../../types/crypto';

/**
 * AES-256-GCM encryption implementation
 */
export class EncryptionService {
  private static readonly config = CRYPTO_CONFIG.aes;

  /**
   * Encrypt plaintext using AES-256-GCM
   */
  static async encrypt(plaintext: string, key: CryptoKey): Promise<EncryptionResult> {
    try {
      // Generate secure random IV
      const iv = this.generateIV();
      
      // Convert plaintext to bytes
      const plaintextBytes = new TextEncoder().encode(plaintext);
      
      // Encrypt using Web Crypto API
      const encryptedBuffer = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv,
          tagLength: this.config.tagLength * 8, // Convert to bits
        },
        key,
        plaintextBytes
      );
      
      // Split ciphertext and authentication tag
      const encryptedBytes = new Uint8Array(encryptedBuffer);
      const tagLength = this.config.tagLength;
      const ciphertext = encryptedBytes.slice(0, -tagLength);
      const tag = encryptedBytes.slice(-tagLength);
      
      return {
        ciphertext: this.arrayBufferToBase64(ciphertext),
        iv: this.arrayBufferToBase64(iv),
        tag: this.arrayBufferToBase64(tag),
      };
    } catch (error) {
      throw new CryptoError(`Encryption failed: ${error.message}`, 'ENCRYPTION_FAILED', 'critical');
    }
  }

  /**
   * Decrypt ciphertext using AES-256-GCM
   */
  static async decrypt(encrypted: DecryptionInput, key: CryptoKey): Promise<string> {
    try {
      // Decode from base64
      const ciphertext = this.base64ToArrayBuffer(encrypted.ciphertext);
      const iv = this.base64ToArrayBuffer(encrypted.iv);
      const tag = this.base64ToArrayBuffer(encrypted.tag);
      
      // Combine ciphertext and tag for Web Crypto API
      const encryptedData = new Uint8Array(ciphertext.length + tag.length);
      encryptedData.set(new Uint8Array(ciphertext), 0);
      encryptedData.set(new Uint8Array(tag), ciphertext.length);
      
      // Decrypt using Web Crypto API
      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv,
          tagLength: this.config.tagLength * 8, // Convert to bits
        },
        key,
        encryptedData
      );
      
      // Convert to string
      return new TextDecoder().decode(decryptedBuffer);
    } catch (error) {
      throw new CryptoError(`Decryption failed: ${error.message}`, 'DECRYPTION_FAILED', 'critical');
    }
  }

  /**
   * Generate a secure random IV
   */
  static generateIV(): Uint8Array {
    return crypto.getRandomValues(new Uint8Array(this.config.ivLength));
  }

  /**
   * Verify encryption/decryption roundtrip
   */
  static async verifyRoundtrip(plaintext: string, key: CryptoKey): Promise<boolean> {
    try {
      const encrypted = await this.encrypt(plaintext, key);
      const decrypted = await this.decrypt(encrypted, key);
      return plaintext === decrypted;
    } catch {
      return false;
    }
  }

  /**
   * Convert ArrayBuffer to Base64 string
   */
  private static arrayBufferToBase64(buffer: ArrayBuffer | Uint8Array): string {
    const bytes = buffer instanceof ArrayBuffer ? new Uint8Array(buffer) : buffer;
    let binary = '';
    for (let i = 0; i < bytes.length; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * Convert Base64 string to ArrayBuffer
   */
  private static base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }
}

/**
 * Custom CryptoError class
 */
class CryptoError extends Error {
  constructor(
    message: string,
    public code: string,
    public severity: 'low' | 'medium' | 'high' | 'critical'
  ) {
    super(message);
    this.name = 'CryptoError';
  }
}

// Convenience functions
export const encryptData = EncryptionService.encrypt;
export const decryptData = EncryptionService.decrypt;
export const generateIV = EncryptionService.generateIV;